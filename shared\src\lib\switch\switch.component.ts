import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { FormsModule } from '@angular/forms';
@Component({
  selector: 'lib-switch',
  standalone: true,
  imports: [CommonModule, NzSwitchModule, FormsModule],
  template: `
    <nz-switch
      [(ngModel)]="switchValue"
      [nzCheckedChildren]="checkedTemplate"
      [nzUnCheckedChildren]="unCheckedTemplate"
      [nzDisabled]="!switchEnable"
      (ngModelChange)="emitClickEvent()"
    ></nz-switch>
    <ng-template #checkedTemplate
      ><span nz-icon nzType="eye"></span
    ></ng-template>
    <ng-template #unCheckedTemplate
      ><span nz-icon nzType="eye-invisible"></span
    ></ng-template>
  `,
  styles: [
    `
      :host ::ng-deep .ant-switch-checked {
        background-color: #0070cc;
      }
    `,
  ],
})
export class SwitchComponent {
  @Input() switchEnable = true;
  @Input() switchValue = false;
  @Output() switchValueChange = new EventEmitter<boolean>();
  @Output() switchClicked = new EventEmitter<boolean>();

  emitClickEvent(): void {
    this.switchValueChange.emit(this.switchValue);
  }
}
