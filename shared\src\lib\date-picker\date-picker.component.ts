import {
  Component,
  Input,
  AfterViewInit,
  Renderer2,
  forwardRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { noop } from 'rxjs';

@Component({
  selector: 'lib-date-picker',
  standalone: true,
  imports: [CommonModule, FormsModule, NzDatePickerModule],
  templateUrl: './date-picker.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DatePickerComponent),
      multi: true,
    },
  ],
})
export class DatePickerComponent
  implements AfterViewInit, ControlValueAccessor
{
  //使用組件時的defaultTime，預設為當前時間
  @Input() showTime = false;
  @Input() size: 'large' | 'small' | 'default' = 'large';

  value: Date = new Date();
  onChange: (value: Date) => void = noop;
  onTouched = noop;

  constructor(private renderer: Renderer2) {}

  ngAfterViewInit() {
    const style = this.renderer.createElement('style');
    const text = this.renderer.createText(`
      #input {
        height: 30px;
      }
      .ant-picker-panel-container .ant-picker-footer .ant-picker-ok button {
        width: 60px !important;
        height: 30px !important;
        font-size: 16px !important;
      }
    `);
    this.renderer.appendChild(style, text);
    this.renderer.appendChild(document.head, style);
  }

  writeValue(value: Date): void {
    if (value) {
      this.value = value;
    } else {
      this.value = new Date();
    }
  }

  registerOnChange(fn: (value: Date) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  onChangeValue(value: Date): void {
    this.value = value;
    this.onChange(value);
    this.onTouched();
  }

  onOk(result: Date | Date[] | null): void {
    console.log('onOk', result);
  }
}
