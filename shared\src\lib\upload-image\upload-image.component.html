<div class="clearfix">
  <nz-upload
    class="uploader"
    [nzAccept]="'.png, .jpg, .jpeg'"
    [nzCustomRequest]="customUpload"
    [(nzFileList)]="fileList"
    [nzShowButton]="fileList.length < 1"
    [nzShowUploadList]="true"
    [nzName]="filename"
    nzListType="picture-card"
    [nzBeforeUpload]="beforeUpload"
    (nzChange)="handleChange($event)"
    [nzPreview]="handlePreview"
  >
    <div>
      <span nz-icon nzType="plus"></span>
      <div style="margin-top: 8px">Upload</div>
    </div>
  </nz-upload>
  <nz-modal
    [nzVisible]="previewVisible"
    [nzContent]="modalContent"
    [nzFooter]="null"
    (nzOnCancel)="previewVisible = false"
  >
    <ng-template #modalContent>
      <img
        [src]="previewImage"
        [ngStyle]="{ width: '100%' }"
        alt="Preview Image"
      />
    </ng-template>
  </nz-modal>
</div>
