import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router, ParamMap } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { ProductService } from '../product.service';
import { Product } from '@models/product.model';
import { environment } from '../../../../environments/environment';
import { NzMessageService } from 'ng-zorro-antd/message';
import { CategoryService } from '../../category/category.service';
import { Category } from '@models/category.model';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { InputComponent } from '@lib/input/input.component';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { ModalComponent } from '@lib/modal/modal.component';
import { UploadImageComponent } from '@lib/upload-image/upload-image.component';
@Component({
  templateUrl: './product-index.component.html',
  imports: [
    NzFormModule,
    FormsModule,
    ReactiveFormsModule,
    NzSelectModule,
    InputComponent,
    NzFlexModule,
    ModalComponent,
    UploadImageComponent,
  ],
})
export class ProductIndexComponent implements OnInit {
  constructor(
    private route: ActivatedRoute,
    private productService: ProductService,
    private categoryService: CategoryService,
    private router: Router,
    private message: NzMessageService
  ) {}
  validateForm = new FormGroup({
    productId: new FormControl<number | null>(-1),
    productName: new FormControl<string | null>(null, [Validators.required]),
    categories: new FormControl<number[] | null>([], [Validators.required]),
    imagePath: new FormControl<string | null>(null, [Validators.required]),
    productPrice: new FormControl<number | null>(null),
    productBrand: new FormControl<string | null>(null),
    productSpec: new FormControl<string | null>(null, [Validators.required]),
    productDesc: new FormControl<string | null>(null),
  });
  categories: Category[] = [];
  fileList: NzUploadFile[] = [];
  productIndex: string | null = null;
  product: Product | null = null;

  ngOnInit(): void {
    this.getCategories();
    this.fileList = [];
    this.route.paramMap.subscribe((params: ParamMap) => {
      this.productIndex = params.get('productId');
      if (this.productIndex && this.productIndex !== 'new') {
        this.productService
          .getProducts(this.productIndex)
          .subscribe((response) => {
            this.product = response.data[0];
            const patchProduct = {
              ...this.product,
              categories: Array.isArray(this.product.categories)
                ? this.product.categories
                : [this.product.categories]
            };
            this.validateForm.patchValue(patchProduct);
            // 綁定圖片
            this.fileList = [
              {
                uid: this.product.productId.toString(),
                name: this.product.productName,
                url:
                  environment.imageUrl + '/product/' + this.product.imagePath,
              },
            ];
          });
      }
    });
  }

  submitForm(): void {
    if (this.validateForm.valid) {
      const formValue = { ...this.validateForm.value };

      if (this.productIndex && this.productIndex !== 'new') {
        this.productService.updateProduct(formValue).subscribe((response) => {
          if (response.success) {
            this.message.success('更新商品成功');
            this.router.navigate(['/product']);
          }
        });
      } else {
        this.productService.createProduct(formValue).subscribe((response) => {
          if (response.success) {
            this.message.success('新增商品成功');
            this.router.navigate(['/product']);
          }
        });
      }
    } else {
      this.message.error('請輸入正確資料');
    }
  }

  uploadImage(filename: string) {
    this.validateForm.patchValue({ imagePath: filename });
    this.validateForm.get('imagePath')?.updateValueAndValidity();
  }

  handleDeleteClick() {
    if (this.productIndex) {
      this.productService
        .deleteProduct(this.productIndex)
        .subscribe((response) => {
          if (response.success) {
            this.message.success('刪除商品成功');
          }
          this.router.navigate(['/product']);
        });
    }
  }

  getCategories() {
    this.categoryService.getCategory().subscribe((response) => {
      this.categories = response.data;
    });
  }
}
