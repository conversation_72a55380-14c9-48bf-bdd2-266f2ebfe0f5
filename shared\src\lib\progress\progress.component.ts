import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzProgressModule } from 'ng-zorro-antd/progress';

@Component({
  selector: 'lib-progress',
  standalone: true,
  imports: [CommonModule, NzProgressModule],
  template: `
    <nz-progress
      [nzPercent]="percent"
      nzSize="small"
      [nzStrokeColor]="{ '0%': '#108ee9', '100%': '#87d068' }"
      [nzFormat]="formatPercent"
    ></nz-progress>
  `,
})
export class ProgressComponent {
  @Input() percent = 0;

  formatPercent = (percent: number) => `${percent.toFixed(2)}%`;
}
