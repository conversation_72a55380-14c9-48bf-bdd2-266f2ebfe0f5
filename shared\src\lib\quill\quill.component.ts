import { Component, ElementRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import Quill from 'quill';

@Component({
  selector: 'lib-quill',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div id="quill" class="quill-rounded" #quillContainer style="height: 200px;"></div>
  `,
  styles: [
    `
      .quill-rounded {
        background-color: #fff;
        border-radius: 0px 0px 12px 12px;
        overflow: hidden;
      }
      .ql-toolbar {
        background-color: #fff; /* 修改編輯器工具列的背景顏色 */
        border-radius: 12px 12px 0px 0px;
        overflow: hidden;
      }
    `,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class QuillComponent {
  @ViewChild('quillContainer') quillContainer!: ElementRef;
  private quillEditor: any;

  content: string = ''; // 初始化編輯器內容為空字串

  ngAfterViewInit() {
    this.initQuillEditor();

    // 監聽編輯器內容變化事件，並將變化同步到 Angular 的資料模型
    this.quillEditor.on('text-change', () => {
      this.content = this.quillEditor.root.innerHTML;
    });
  }

  initQuillEditor(): void {
    const toolbarOptions = {
      container: [
        ['bold', 'italic', 'underline', 'strike'], // 預設的工具按鈕
        [{ header: 1 }, { header: 2 }], // 預設的工具按鈕
        [{ list: 'ordered' }, { list: 'bullet' }], // 預設的工具按鈕
        [{ script: 'sub' }, { script: 'super' }], // 預設的工具按鈕
        [{ indent: '-1' }, { indent: '+1' }], // 預設的工具按鈕
        [{ direction: 'rtl' }], // 預設的工具按鈕
        [{ size: ['small', false, 'large', 'huge'] }], // false 是 normal 的意思
        [{ header: [1, 2, 3, 4, 5, 6, false] }], // 預設的工具按鈕
        [{ color: [] }, { background: [] }], // 預設的工具按鈕
        [{ font: [] }], // 預設的工具按鈕
        ['image'], // 自定義的內建工具按鈕
        ['clean'], // 預設的工具按鈕
      ],
      handlers: {
        customButton: () => console.log('handle custom button'),
      },
    };
    this.quillEditor = new Quill(this.quillContainer.nativeElement, {
      // Quill Editor 的配置
      theme: 'snow', // 可以選擇不同的主題，例如 'bubble' 或 'core'
      modules: {
        toolbar: toolbarOptions,
      },
    });
  }

  getQuillEditorInstance(): Quill {
    return this.quillEditor!;
  }
}
