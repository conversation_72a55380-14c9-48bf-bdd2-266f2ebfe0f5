import { Component, Input, Output, EventEmitter, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  HttpClient,
  HttpEvent,
  HttpEventType,
  HttpRequest,
  HttpResponse,
} from '@angular/common/http';
import { Observable, Observer, tap } from 'rxjs';

import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzUploadFile, NzUploadXHRArgs } from 'ng-zorro-antd/upload';

const getBase64 = (file: File): Promise<string | ArrayBuffer | null> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

@Component({
  selector: 'lib-upload-image',
  standalone: true,
  imports: [
    CommonModule,
    NzUploadModule,
    NzIconModule,
    NzModalModule,
    NzButtonModule,
  ],
  templateUrl: './upload-image.component.html',
  styles: [
    `
      :host ::ng-deep .ant-upload.ant-upload-select-picture-card,
      :host ::ng-deep .ant-upload-list-picture-card-container {
        width: 200px;
        height: 200px;
      }
    `,
  ],
})
export class UploadImageComponent {
  constructor(
    private message: NzMessageService,
    private http: HttpClient,
    @Inject('API_URL') private baseApiUrl: string
  ) {}
  @Input() avatarUrl?: string;
  @Input() fileList: NzUploadFile[] = [];
  @Input() uploadFolder = '';
  @Output() uploadEvent = new EventEmitter<string>();
  @Output() fileListRemove = new EventEmitter<NzUploadFile[]>();

  loading = false;
  filename = '';

  customUpload = (item: NzUploadXHRArgs) => {
    // 在這裡實現自定義的上傳邏輯
    console.log(item);
    const formData = new FormData();
    formData.append('file', item.file as any);
    const req = new HttpRequest(
      'POST',
      `${this.baseApiUrl}/Upload/${this.uploadFolder}`,
      formData,
      {
        reportProgress: true,
      }
    );

    return this.http
      .request(req)
      .pipe(
        tap({
          next: (event: HttpEvent<any>) => {
            if (event.type === HttpEventType.UploadProgress) {
              if (event.total! > 0) {
                (event as any).percent = (event.loaded / event.total!) * 100;
              }
              item.onProgress!(event, item.file!);
            } else if (event instanceof HttpResponse) {
              item.onSuccess!(event.body, item.file!, event);
            }
          },
          error: (err) => {
            item.onError!(err, item.file!);
          },
        })
      )
      .subscribe();
  };

  beforeUpload = (
    file: NzUploadFile,
    _fileList: NzUploadFile[]
  ): Observable<boolean> =>
    new Observable((observer: Observer<boolean>) => {
      const isJpgOrPng =
        file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        this.message.error('請上傳正確的圖片格式!');
        observer.complete();
        return;
      }
      const isLt2M = file.size! / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.message.error('圖片大小不能超過2MB!');
        observer.complete();
        return;
      }
      observer.next(isJpgOrPng && isLt2M);
      observer.complete();
    });

  async handleChange(info: {
    file: NzUploadFile;
    fileList: NzUploadFile[];
  }): Promise<void> {
    this.fileList = info.fileList;
    switch (info.file.status) {
      case 'uploading':
        this.loading = true;
        break;
      case 'done':
        try {
          console.log(info.file.originFileObj);
          const img = await getBase64(info.file.originFileObj!);
          this.loading = false;
          this.avatarUrl = img as string;
          // 使用後端回傳的 fileName
          this.filename = info.file.response?.fileName || info.file.name || '';
          this.uploadEvent.emit(this.filename);
        } catch (error) {
          this.message.error('圖片處理失敗');
          this.loading = false;
        }
        break;
      case 'error':
        this.message.error('網路錯誤');
        this.loading = false;
        break;
      case 'removed':
        this.fileList = this.fileList.filter(
          (file) => file.uid !== info.file.uid
        );
        this.fileListRemove.emit(this.fileList);
        break;
    }
  }

  previewImage: string | undefined = '';
  previewVisible = false;

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    console.log(file);
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64(file.originFileObj!);
    }
    console.log(file['preview']);
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };
}
