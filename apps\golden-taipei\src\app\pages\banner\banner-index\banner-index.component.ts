import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, ParamMap } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';

import { NzUploadFile } from 'ng-zorro-antd/upload';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzFlexModule } from 'ng-zorro-antd/flex';

import { environment } from '../../../../environments/environment';
import { BannerService } from '../banner.service';
import { Banner } from '@models/banner.model';
import { UploadImageComponent } from '@lib/upload-image/upload-image.component';
import { SwitchComponent } from '@lib/switch/switch.component';
import { InputComponent } from '@lib/input/input.component';
import { ModalComponent } from '@lib/modal/modal.component';
@Component({
  selector: 'app-banner-index',
  standalone: true,
  imports: [
    CommonModule,
    NzFormModule,
    UploadImageComponent,
    SwitchComponent,
    InputComponent,
    ModalComponent,
    NzFlexModule,
    ReactiveFormsModule,
  ],
  templateUrl: './banner-index.component.html',
})
export class BannerIndexComponent implements OnInit {
  constructor(
    private bannerService: BannerService,
    private route: ActivatedRoute,
    private message: NzMessageService,
    private router: Router
  ) {}
  fileList: NzUploadFile[] = [];
  switchValue = false;
  validateForm = new FormGroup({
    bannerId: new FormControl<number>(-1),
    imagePath: new FormControl<string | null>(null, [Validators.required]),
    link: new FormControl<string | null>(null),
    enable: new FormControl<boolean>(false),
  });
  banner: Banner | null = null;
  bannerId: string | null = null;
  ngOnInit(): void {
    this.route.paramMap.subscribe((params: ParamMap) => {
      this.bannerId = params.get('bannerId');
      if (this.bannerId && this.bannerId !== 'new') {
        // 商家資料
        this.bannerService.getBanner(this.bannerId).subscribe((response) => {
          this.banner = response.data[0];
          this.switchValue = this.banner.enable;
          this.validateForm.patchValue({
            bannerId: this.banner.bannerId,
            imagePath: this.banner.imagePath,
            link: this.banner.link,
            enable: this.banner.enable,
          });
          // 綁定圖片
          this.fileList = [
            {
              uid: this.banner.bannerId.toString(),
              name: this.banner.imagePath,
              url: environment.imageUrl + '/banner/' + this.banner.imagePath,
            },
          ];
        });
      } else {
        this.validateForm.reset({ bannerId: -1 });
      }
    });
  }

  uploadImage(filename: string) {
    this.validateForm.patchValue({ imagePath: filename });
    this.validateForm.get('imagePath')?.updateValueAndValidity();
  }

  switchClicked(value: boolean) {
    this.switchValue = value;
  }

  submitForm() {
    if (this.validateForm.valid) {
      this.validateForm.patchValue({ enable: this.switchValue });
      const formValue = { ...this.validateForm.value };
      console.log(formValue);

      if (this.bannerId && this.bannerId !== 'new') {
        this.bannerService.updateBanner(formValue).subscribe((response) => {
          if (response.success) {
            this.message.success('更新廣告橫幅資料成功');
            this.router.navigate(['/banner']);
          }
        });
      } else {
        this.bannerService.createBanner(formValue).subscribe((response) => {
          if (response.success) {
            this.message.success('新增廣告橫幅資料成功');
            this.router.navigate(['/banner']);
          }
        });
      }
    }
  }

  handleDeleteClick() {
    if (this.bannerId) {
      this.bannerService.deleteBanner(this.bannerId).subscribe((response) => {
        if (response.success) {
          this.message.success('刪除廣告橫幅資料成功');
          this.router.navigate(['/banner']);
        }
      });
    }
  }
}
