{"name": "angular-admin", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/angular-admin/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/angular-admin", "index": "apps/angular-admin/src/index.html", "baseHref": "/Admin/", "deployUrl": "/Admin/", "browser": "apps/angular-admin/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/angular-admin/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/angular-admin/public"}, {"glob": "**/*", "input": "./node_modules/.pnpm/node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["apps/angular-admin/src/styles.less", "node_modules/quill/dist/quill.snow.css"], "stylePreprocessorOptions": {"includePaths": ["shared/src/styles/themes"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "angular-admin:build:production"}, "development": {"buildTarget": "angular-admin:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "angular-admin:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/angular-admin/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "angular-admin:build", "staticFilePath": "dist/apps/angular-admin/browser", "spa": true}}}}