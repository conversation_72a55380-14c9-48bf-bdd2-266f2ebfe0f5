import { DatePipe } from '@angular/common';
import { Injectable } from '@angular/core';
import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root'
})
export class DownloadFormService {
  constructor(private datePipe: DatePipe) {}
  async TableDownload(title: string, listOfColumns: any[], filteredTable: any[]): Promise<void> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');

    // 添加標題行
    const headers = listOfColumns
      .filter(column => column.key !== undefined)
      .map(column => column.title);
    worksheet.addRow(headers);

    // 添加資料行
    filteredTable.forEach(member => {
      const row: (string | number | boolean)[] = [];
      for (const column of listOfColumns) {
        if (column.key === undefined) {
          continue;
        }
        const key = column.key;
        let value = member[key];

        if (typeof value === 'boolean') {
          value = value ? '是' : '否';
        }

        // 替換值中的換行符和逗號
        if (typeof value === 'string') {
          value = value.replace(/\n/g, ' ').replace(/\r/g, ' ').replace(/,/g, ' ');
          const dateValue = new Date(value);
          if (!isNaN(dateValue.getTime())) {
            // 如果是有效的日期，則進行格式化
            value = this.datePipe.transform(dateValue, 'yyyy-MM-dd HH:mm:ss') || value;
          }
        }

        row.push(value as string | number | boolean); // 明確轉換類型
      }
      worksheet.addRow(row);
    });
    
    worksheet.columns.forEach(column => {
      const lengths = column.values?.map(v => v?.toString().length) || [];
      const validLengths = lengths.filter((v): v is number => typeof v === 'number');
      const maxLength = validLengths.length > 0 ? Math.max(...validLengths) : 10;
      column.width = Math.max(maxLength, 20);
    });

    // 寫入文件
    workbook.xlsx.writeBuffer().then(buffer => {
      const blob = new Blob([buffer], { type: 'application/octet-stream' });
      const now = new Date();
      const formattedDate = this.datePipe.transform(now, 'yyyyMMdd_HHmmss');
      saveAs(blob, `${title}_${formattedDate}.xlsx`);
    }).catch(error => {
      console.error('Error writing Excel file', error);
    });
  }
  
}
