{"name": "golden-taipei", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/golden-taipei/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/golden-taipei", "index": "apps/golden-taipei/src/index.html", "baseHref": "/Admin/", "deployUrl": "/Admin/", "browser": "apps/golden-taipei/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/golden-taipei/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/golden-taipei/public"}, {"glob": "**/*", "input": "./node_modules/.pnpm/node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["apps/golden-taipei/src/styles.less"], "stylePreprocessorOptions": {"includePaths": ["shared/src/styles/themes"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "baseHref": "/golden-taipei/Admin/", "deployUrl": "/golden-taipei/Admin/", "fileReplacements": [{"replace": "apps/golden-taipei/src/environments/environment.ts", "with": "apps/golden-taipei/src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "golden-taipei:build:production"}, "development": {"buildTarget": "golden-taipei:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "golden-taipei:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/golden-taipei/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "golden-taipei:build", "staticFilePath": "dist/apps/golden-taipei/browser", "spa": true}}}}