import { Component, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { FormsModule } from '@angular/forms';
@Component({
  selector: 'lib-range-picker',
  standalone: true,
  imports: [CommonModule, NzDatePickerModule, FormsModule],
  template: `
    <nz-range-picker
      [(ngModel)]="value"
      (ngModelChange)="onChange($event)"
      [nzAllowClear]="true"
    ></nz-range-picker>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RangePickerComponent),
      multi: true,
    },
  ],
})
export class RangePickerComponent implements ControlValueAccessor {
  value: Date[] = [];
  onChange = (_: any) => {
    /* empty */
  };
  onTouched = () => {
    /* empty */
  };

  writeValue(value: Date[]): void {
    if (value) {
      this.value = value;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  onModelChange(value: Date[]): void {
    if (value && value.length === 2) {
      value[0].setHours(0, 0, 0, 0);
      value[1].setHours(23, 59, 59, 999);
    }
    this.value = value;
    this.onChange(value);
    this.onTouched();
  }
}
