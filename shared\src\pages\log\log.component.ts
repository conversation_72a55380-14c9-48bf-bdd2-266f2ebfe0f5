import { Component, OnInit } from '@angular/core';
import { Log } from '../../models/log.model';
import { LogService } from '../../services/log.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzTableModule } from 'ng-zorro-antd/table';
import { TagComponent } from '@lib/tag/tag.component';
import { NzTypographyModule } from 'ng-zorro-antd/typography';
import { DatePipe } from '@angular/common';
@Component({
  selector: 'lib-log',
  standalone: true,
  imports: [
    NzTableModule,
    TagComponent,
    NzTypographyModule,
    DatePipe
  ],
  templateUrl: './log.component.html',
  styles: [
    `
      .scrollable-container {
        max-height: 100px;
        overflow-y: auto;
        white-space: pre-wrap;
      }
    `
  ],
})
export class LogComponent implements OnInit {
  constructor(private logService: LogService, private message: NzMessageService) { }
  title = '系統日誌';
  logs: Log[] = [];
  listOfColumns: any[] = [
    {
      title: '發生時間',
      compare: (a: Log, b: Log) => a.timestamp.getTime() - b.timestamp.getTime(),
      width: '15%',
    },
    {
      title: '類型',
      compare: (a: Log, b: Log) => a.logLevel.localeCompare(b.logLevel),
      width: '5%',
    },
    {
      title: '訊息',
      compare: (a: Log, b: Log) => a.message.localeCompare(b.message),
      width: '40%',
    },
    {
      title: '來源',
      compare: (a: Log, b: Log) => a.source.localeCompare(b.source),
      width: '15%',
    },
    {
      title: '用戶',
      compare: (a: Log, b: Log) => a.userId.localeCompare(b.userId),
      width: '5%',
    },
    {
      title: '機器名稱',
      compare: (a: Log, b: Log) => a.machineName.localeCompare(b.machineName),
      width: '10%',
    },
  ];
  ngOnInit(): void {
    this.loadLogs();
  }

  loadLogs(): void {
    this.logService.getLogs().subscribe({
      next: (response) => {
        this.logs = response.data; 
      },
      error: (err) => {
        this.message.create('error', '獲取系統日誌失敗: ' + err.error.message);
      }
    });
  }
}
