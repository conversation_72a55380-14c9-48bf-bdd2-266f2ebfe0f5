<div class="input-wrapper">
  <ng-container [ngSwitch]="type">
    <textarea
      *ngSwitchCase="'textarea'"
      nz-input
      [(ngModel)]="value"
      (ngModelChange)="onChanged($event)"
      (blur)="onTouched()"
      [disabled]="disabled"
      [placeholder]="placeholder"
      [rows]="rows"
      class="w-full"
    >
    </textarea>

    <input
      *ngSwitchDefault
      nz-input
      [(ngModel)]="value"
      (ngModelChange)="onChanged($event)"
      (blur)="onTouched()"
      [disabled]="disabled"
      [placeholder]="placeholder"
      class="w-full"
    />
  </ng-container>
</div>
