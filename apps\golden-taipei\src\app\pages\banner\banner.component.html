<div class="col-span-2 flex items-center gap-4">
  <h2 class="text-2xl font-semibold mb-0">廣告橫幅管理</h2>
  <lib-button
    [buttonType]="'primary'"
    [shape]="'round'"
    [iconType]="'plus'"
    [routerLink]="['/banner', 'new']"
  >
    新增廣告橫幅
  </lib-button>
</div>
<br />
<div class="w-full overflow-x-auto shadow-sm rounded-lg">
  <nz-table
    #bannerTable
    [nzData]="banners"
    nzTableLayout="fixed"
    [nzPageSize]="15"
    [nzSize]="'small'"
    class="min-w-full bg-white"
  >
    <thead>
      <tr class="bg-gray-50 border-b">
        @for (column of listOfColumns; track column.title) {
        <th
          [nzSortFn]="column.compare!"
          [nzWidth]="column.width"
          class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider"
        >
          {{ column.title }}
        </th>
        }
      </tr>
    </thead>
    <tbody
      cdkDropList
      class="divide-y divide-gray-200"
      (cdkDropListDropped)="drop($event)"
      [cdkDropListData]="banners"
    >
      @for (banner of banners; track banner.bannerId) {
      <tr
        cdkDrag
        class="hover:bg-gray-50 transition-colors"
        [cdkDragData]="banner"
      >
        <div
          *cdkDragPreview
          [class]="'bg-white border shadow-md rounded-md p-2'"
        >
          <tr class="flex items-center gap-4">
            <td class="px-4 py-3">
              <lib-image
                [src]="imageUrl + '/Banner/' + banner.imagePath"
                class="max-w-[400px] w-full max-h-[100px] rounded-md"
              ></lib-image>
            </td>
            <td class="px-4 py-3">{{ banner.link }}</td>
          </tr>
        </div>
        <div
          *cdkDragPlaceholder
          [class]="
            'h-[52px] bg-gray-100 border-2 border-dashed border-gray-300 rounded-md'
          "
        ></div>
        <td cdkDragHandle style="cursor: move">
          <span nz-icon nzType="drag" nzTheme="outline"></span>
        </td>
        <td class="px-4 py-3 whitespace-nowrap">
          <div class="flex gap-3">
            <lib-button
              [iconType]="'edit'"
              [buttonType]="'primary'"
              [routerLink]="['/banner', banner.bannerId]"
            ></lib-button>
            <lib-modal
              [iconType]="'delete'"
              [danger]="true"
              [buttonType]="'default'"
              [description]="'是否要刪除此廣告橫幅？'"
              (nzOnOkClick)="handleDeleteClick(banner.bannerId)"
            ></lib-modal>
          </div>
        </td>
        <td class="px-4 py-3">
          <lib-image
            [src]="imageUrl + '/Banner/' + banner.imagePath"
            class="max-w-[400px] w-full max-h-[100px] rounded-md"
          />
        </td>
        <td class="px-4 py-3 text-gray-500 truncate max-w-xs">
          <a [href]="banner.link" target="_blank">{{ banner.link }}</a>
        </td>
        <td class="px-4 py-3 whitespace-nowrap">
          <span
            [class]="
              banner.enable
                ? 'px-2 inline-flex leading-5 font-semibold rounded-full bg-green-100 text-green-800'
                : 'px-2 inline-flex leading-5 font-semibold rounded-full bg-red-100 text-red-800'
            "
          >
            {{ banner.enable ? '啟用' : '停用' }}
          </span>
        </td>
      </tr>
      }
    </tbody>
  </nz-table>
</div>
