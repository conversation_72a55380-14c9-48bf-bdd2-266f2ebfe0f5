import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Contact } from '@models/contact.model';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
@Component({
  templateUrl: './contact-edit.component.html',
  imports: [NzFormModule, NzInputModule, FormsModule, ReactiveFormsModule],
})
export class ContactEditComponent implements OnInit {
  @Input() contact: Contact = new Contact();
  @Output() formSubmit = new EventEmitter<any>();
  validateForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.validateForm = this.fb.group({
      contactId: [-1],
      contactValue: [null, [Validators.required]],
    });
  }

  ngOnInit() {
    this.validateForm.patchValue(this.contact);
  }

  submitForm(): void {
    if (this.validateForm.valid) {
      this.formSubmit.emit(this.validateForm.value);
    } else {
      Object.values(this.validateForm.controls).forEach((control) => {
        control.markAsDirty();
        control.updateValueAndValidity();
      });
    }
  }
}
