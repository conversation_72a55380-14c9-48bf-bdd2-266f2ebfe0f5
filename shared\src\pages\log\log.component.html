<div class="container mt-5">
    <div nz-row class="button-container">
      <h2 class="text-2xl font-semibold mb-0 flex-grow-1">{{title}}</h2>
    </div>
    <br>
    <nz-table #sortTable [nzData]="logs" nzTableLayout="fixed" [nzSize]="'small'">
      <thead>
        <tr>
          @for (column of listOfColumns; track $index) {
            <th [nzSortFn]="column.compare" [nzWidth]="column.width">
              {{ column.title }}
            </th>
          }
        </tr>
      </thead>
      <tbody>
        @for (log of sortTable.data; track log.id) {
          <tr>
            <td>{{ log.timestamp | date:'yyyy-MM-dd HH:mm:ss' }}</td>
            <td>
              <lib-tag [color]="log.logLevel === 'Info' ? 'green' : log.logLevel === 'Error' ? 'red' : 'yellow'">{{log.logLevel}}</lib-tag>
            </td>
            <td>
                <div class="scrollable-container" #target>
                  <p nzCopyable nz-typography
                  [nzContent]="log.message"
                  ></p> 
                </div>
            </td>
            <td>{{ log.source }}</td>
            <td>{{ log.userId }}</td>
            <td>{{ log.machineName }}</td>
          </tr>
        }
      </tbody>
    </nz-table>
  </div>