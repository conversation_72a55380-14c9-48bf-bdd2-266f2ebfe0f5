import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Permission } from '../models/permission.model';
import { ApiResponse } from '../models/api-response.model';
@Injectable({
  providedIn: 'root'
})
export class PermissionService {
  private apiUrl: string;
  constructor(private http: HttpClient, @Inject('API_URL') private baseApiUrl: string) {
    this.apiUrl = `${this.baseApiUrl}/Permission`;
  }

  getPermissions(): Observable<ApiResponse<Permission[]>> {
    return this.http.get<ApiResponse<Permission[]>>(this.apiUrl);
  }

  updatePermission(permission: Permission): Observable<Permission> {
    return this.http.put<Permission>(this.apiUrl + "/UpdateRolePermission/" + permission.index, permission);
  }
}
