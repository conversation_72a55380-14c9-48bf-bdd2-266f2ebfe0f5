import {
  Component,
  Input,
  Output,
  EventEmitter,
  TemplateRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzTabsModule } from 'ng-zorro-antd/tabs';

@Component({
  selector: 'lib-tabs',
  standalone: true,
  imports: [CommonModule, NzTabsModule],
  templateUrl: './tabs.component.html',
  styleUrl: './tabs.component.scss',
})
export class TabsComponent {
  @Input() tabs: any[] = [];
  @Input() content!: TemplateRef<any>;
  @Input() data: any[] = [];
  @Output() tabChanged = new EventEmitter<number>();

  onTabChange(index: number) {
    this.tabChanged.emit(index);
  }

  tabBarExtraContent: TemplateRef<any> | undefined = undefined;
}
