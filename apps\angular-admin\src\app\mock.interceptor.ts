import { HttpInterceptorFn, HttpResponse } from '@angular/common/http';
import { of } from 'rxjs';

export const MockInterceptor: HttpInterceptorFn = (request, next) => {
    // 取得 API 路徑（去除 domain 部分）
    const url = new URL(request.url, window.location.origin).pathname;

    switch (url) {
        case '/User/GetAllUsers':
            return of(new HttpResponse({
                status: 200,
                body: {
                    success: true,
                    data: [
                        {
                            "index": 0,
                            "account": "Admin",
                            "userName": "使用者",
                            "password": "877954863766d485893c939baa2c0478a36dba63f6ab477bc4b6faf2c8ff7808",
                            "role": 1,
                            "roleName": "Administrator",
                            "errorTime": null,
                            "loginTime": "2025-05-28T16:04:57.397",
                            "createTime": null,
                            "createUser": null,
                            "modUser": null,
                            "modTime": "2025-04-17T11:36:10.503",
                            "sort": 1,
                            "enable": true
                        },
                        {
                            "index": 1,
                            "account": "User",
                            "userName": "使用者",
                            "password": "877954863766d485893c939baa2c0478a36dba63f6ab477bc4b6faf2c8ff7808",
                            "role": 2,
                            "roleName": "User",
                            "errorTime": null,
                            "loginTime": "2025-05-28T16:04:57.397",
                            "createTime": null,
                            "createUser": null,
                            "modUser": null,
                            "modTime": "2025-04-17T11:36:10.503",
                            "sort": 1,
                            "enable": true
                        }
                    ]
                }
            }));
        case '/Permission':
            return of(new HttpResponse({
                status: 200,
                body: {
                    success: true,
                    data: [
                        {
                            "index": 0,
                            "page": "log",
                            "role": 1,
                            "roleName": "Administrator",
                            "createFlag": true,
                            "updateFlag": true,
                            "readFlag": true,
                            "deleteFlag": true,
                            "pageName": "異常紀錄"
                        },
                        {
                            "index": 0,
                            "page": "permission",
                            "role": 1,
                            "roleName": "Administrator",
                            "createFlag": true,
                            "updateFlag": true,
                            "readFlag": true,
                            "deleteFlag": true,
                            "pageName": "權限維護管理"
                        },
                        {
                            "index": 0,
                            "page": "user",
                            "role": 1,
                            "roleName": "Administrator",
                            "createFlag": true,
                            "updateFlag": true,
                            "readFlag": true,
                            "deleteFlag": true,
                            "pageName": "帳號維護管理"
                        },

                    ]
                }
            }));
        case '/User/GetAllRoles':
            return of(new HttpResponse({
                status: 200,
                body: {
                    success: true,
                    data: [
                        {
                            "role": 1,
                            "roleName": "Administrator",
                        },
                        {
                            "role": 2,
                            "roleName": "User",
                        }
                    ]
                }
            }));
        case '/Log/GetAllLog':
            return of(new HttpResponse({
                status: 200,
                body: {
                    success: true,
                    data: [
                        {
                            "id": 302,
                            "timestamp": "2025-05-28T16:51:03.603",
                            "logLevel": "Info",
                            "message": "後台登入嘗試: Admin",
                            "exception": null,
                            "source": "/api/User/login",
                            "userId": "Admin",
                            "machineName": "127.0.0.1"
                        },
                        {
                            "id": 279,
                            "timestamp": "2025-05-21T09:42:44.88",
                            "logLevel": "Warning",
                            "message": "Token認證失敗: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'JMt3Ap_4eqYSUTMx8WqPPbBQVXkZ7nj2PltHdfBy9mI'.'. Number of keys in TokenValidationParameters: '1'. \nNumber of keys in Configuration: '0'. \nExceptions caught:\n '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.\ntoken: '[PII of type 'System.IdentityModel.Tokens.Jwt.JwtSecurityToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.\r\n",
                            "exception": null,
                            "source": "/api/Upload/product",
                            "userId": "User",
                            "machineName": "127.0.0.1"
                        }
                    ]
                }
            }));
        default:
            console.log('不符合 mock 條件，繼續處理');
            return next(request);
    }
};