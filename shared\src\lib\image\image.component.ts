import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzImageModule } from 'ng-zorro-antd/image';

@Component({
  selector: 'lib-image',
  standalone: true,
  imports: [CommonModule, NzImageModule],
  template: `
    <img
      nz-image
      [class]="class"
      [style]="style"
      [nzFallback]="fallback"
      [alt]="alt"
      [nzSrc]="src"
      [nzDisablePreview]="disablePreview"
      [loading]="loading"
    />
  `,
})
export class ImageComponent {
  @Input() style = '';
  @Input() src = '';
  @Input() alt = '';
  @Input() disablePreview = true;
  @Input() class = '';
  @Input() loading: 'lazy' | 'eager' = 'lazy';

  fallback = 'https://placehold.co/600x400?text=Not+Found';
}
