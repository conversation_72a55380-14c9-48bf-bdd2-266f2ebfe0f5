import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Log } from '../models/log.model';
import { ApiResponse } from '../models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class LogService {
  constructor(private http: HttpClient, @Inject('API_URL') private baseApiUrl: string) { }

  getLogs(): Observable<ApiResponse<Log[]>> {
    return this.http.get<ApiResponse<Log[]>>(`${this.baseApiUrl}/Log/GetAllLog`);
  }
}
