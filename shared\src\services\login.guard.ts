// login.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, Router } from '@angular/router';
import { LoginService } from './login.service';
import { PermissionCheckService } from '@services/permission-check.service';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoginGuard implements CanActivate {

  constructor(
    private loginService: LoginService,
    private router: Router,
    private permissionCheckService: PermissionCheckService
  ) {}

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean> | boolean {
    if (!this.loginService.isAuthenticated()) {
      this.router.navigate(['/login']);
      return false;
    }

    // 這部分設定在app-routing.module.ts
    const requiredPermission = route.data['permission'] as string;
    if (!requiredPermission) {
      return true;
    }

    return this.permissionCheckService.checkPermission().pipe(
      map(permissionFlags => {
        if (permissionFlags[requiredPermission].readFlag) {
          return true;
        } else {
          this.router.navigate(['/unauthorized']);
          return false;
        }
      })
    );
  }
}