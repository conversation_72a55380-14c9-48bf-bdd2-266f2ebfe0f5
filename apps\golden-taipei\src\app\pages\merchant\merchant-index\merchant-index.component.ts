import { Component, OnInit } from '@angular/core';
import { MerchantService } from '../merchant.service';
import { ActivatedRoute, ParamMap } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { SortMerCat } from '@models/sort-mer-cat.model';
import { Merchant } from '@models/merchant.model';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Router } from '@angular/router';
import { environment } from '../../../../environments/environment';
import { NzFormModule } from 'ng-zorro-antd/form';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { UploadImageComponent } from '@lib/upload-image/upload-image.component';
import { ModalComponent } from '@lib/modal/modal.component';
import {
  CdkDropList,
  CdkDrag,
  CdkDragPlaceholder,
} from '@angular/cdk/drag-drop';
import { InputComponent } from '@lib/input/input.component';
@Component({
  templateUrl: './merchant-index.component.html',
  styleUrls: ['./merchant-index.component.css'],
  imports: [
    NzFormModule,
    FormsModule,
    ReactiveFormsModule,
    NzFlexModule,
    UploadImageComponent,
    CdkDropList,
    CdkDrag,
    CdkDragPlaceholder,
    ModalComponent,
    InputComponent,
  ],
})
export class MerchantIndexComponent implements OnInit {
  constructor(
    private merchantService: MerchantService,
    private route: ActivatedRoute,
    private message: NzMessageService,
    private router: Router
  ) {}
  fileList: NzUploadFile[] = [];
  validateForm = new FormGroup({
    merchantId: new FormControl<number>(-1),
    merchantName: new FormControl<string | null>(null, [Validators.required]),
    imagePath: new FormControl<string | null>(null, [Validators.required]),
  });
  merchant: Merchant | null = null;
  categories: SortMerCat[] = [];
  merchantId: string | null = null;
  ngOnInit(): void {
    this.route.paramMap.subscribe((params: ParamMap) => {
      this.merchantId = params.get('merchantId');
      if (this.merchantId && this.merchantId !== 'new') {
        // 商家資料
        this.merchantService
          .getMerchant(this.merchantId)
          .subscribe((response) => {
            this.merchant = response.data[0];
            this.validateForm.patchValue({
              merchantId: this.merchant.merchantId,
              merchantName: this.merchant.merchantName,
              imagePath: this.merchant.imagePath,
            });
            // 綁定圖片
            this.fileList = [
              {
                uid: this.merchant.merchantId.toString(),
                name: this.merchant.merchantName,
                url:
                  environment.imageUrl + '/merchant/' + this.merchant.imagePath,
              },
            ];
          });
        // 商家排序
        this.merchantService
          .getSortMerCat(this.merchantId)
          .subscribe((response) => {
            this.categories = response.data;
          });
      } else {
        this.validateForm.reset({ merchantId: -1 });
        // 商家排序(取得預設)
        this.merchantService.getSortMerCat(undefined).subscribe((response) => {
          this.categories = response.data;
        });
      }
    });
  }

  uploadImage(filename: string) {
    this.validateForm.patchValue({ imagePath: filename });
    this.validateForm.get('imagePath')?.updateValueAndValidity();
  }

  submitForm() {
    if (this.validateForm.valid) {
      const formValue = { ...this.validateForm.value };
      console.log(formValue);
      // 商家排序
      this.categories.forEach((category) => {
        category.sort = this.categories.indexOf(category);
        category.merchantId = this.merchantId ? parseInt(this.merchantId) : 0;
      });

      if (this.merchantId && this.merchantId !== 'new') {
        // 商家資料
        this.merchantService.updateMerchant(formValue).subscribe();
        this.merchantService
          .insertSortMerCat(this.categories)
          .subscribe((response) => {
            if (response.success) {
              this.message.success('更新商家資料成功');
              this.router.navigate(['/merchant']);
            }
          });
      } else {
        this.merchantService.createMerchant(formValue).subscribe((response) => {
          if (response.success) {
            this.categories.forEach((category) => {
              category.merchantId = response.data;
            });
            this.merchantService
              .insertSortMerCat(this.categories)
              .subscribe((response) => {
                if (response.success) {
                  this.message.success('新增商家資料成功');
                  this.router.navigate(['/merchant']);
                }
              });
          }
        });
      }
    }
  }

  handleDeleteClick() {
    if (this.merchantId) {
      this.merchantService
        .deleteMerchant(this.merchantId)
        .subscribe((response) => {
          if (response.success) {
            this.message.success('刪除商家資料成功');
            this.router.navigate(['/merchant']);
          }
        });
    }
  }

  drop(event: CdkDragDrop<SortMerCat[]>) {
    moveItemInArray(this.categories, event.previousIndex, event.currentIndex);
  }
}
