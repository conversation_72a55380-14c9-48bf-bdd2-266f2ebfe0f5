<form nz-form [formGroup]="validateForm" [nzLayout]="'vertical'">
  <nz-form-item>
    <nz-form-label [nzSpan]="24" [nzLg]="4" nzRequired nzFor="productName">
      商品名稱
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20" nzErrorTip="請輸入商品名稱!">
      <lib-input
        [formControl]="validateForm.controls['productName']"
        placeholder="請輸入商品名稱"
      >
      </lib-input>
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-label [nzSpan]="24" [nzLg]="4" nzRequired nzFor="categories">
      商品分類
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20" nzErrorTip="請選擇商品分類!">
      <nz-select
        formControlName="categories"
        nzSize="large"
        nzMode="multiple"
        [nzMaxTagPlaceholder]="tagPlaceHolder"
        nzPlaceHolder="請選擇商品分類"
      >
        @for (category of categories; track category.categoryId) {
          <nz-option
            [nzValue]="category.categoryId"
            [nzLabel]="category.categoryName"
          >
          </nz-option>
        }
      </nz-select>
      <ng-template #tagPlaceHolder let-selectedList>
        以及其他 {{ selectedList.length }} 項
      </ng-template>
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-label
      [nzSpan]="24"
      [nzLg]="4"
      nzRequired
      nzFor="imagePath"
      nzTooltipTitle="欲上傳照片，請先刪除現有照片"
    >
      商品封面照
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20">
      <lib-upload-image
        [fileList]="fileList"
        uploadFolder="product"
        (uploadEvent)="uploadImage($event)"
      />
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-label [nzSpan]="24" [nzLg]="4" nzFor="productPrice">
      商品價格
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20" nzErrorTip="請輸入商品價格!">
      <lib-input
        [formControl]="validateForm.controls['productPrice']"
        placeholder="請輸入商品價格"
      >
      </lib-input>
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-label [nzSpan]="24" [nzLg]="4" nzFor="productBrand">
      商品品牌
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20" nzErrorTip="請輸入商品品牌!">
      <lib-input
        [formControl]="validateForm.controls['productBrand']"
        placeholder="請輸入商品品牌"
      >
      </lib-input>
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-label [nzSpan]="24" [nzLg]="4" nzRequired nzFor="productSpec">
      商品規格
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20" nzErrorTip="請輸入商品規格!">
      <lib-input
        [formControl]="validateForm.controls['productSpec']"
        placeholder="請輸入商品規格"
      >
      </lib-input>
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-label [nzSpan]="24" [nzLg]="4" nzFor="productDesc">
      商品描述
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20">
      <lib-input
        [formControl]="validateForm.controls['productDesc']"
        type="textarea"
        [rows]="4"
        placeholder="請輸入商品描述"
      >
      </lib-input>
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-control [nzOffset]="0" [nzSpan]="24" [nzLg]="20">
      <div nz-flex [nzGap]="30">
        <lib-modal
          description="是否儲存此商品?"
          [modalConfig]="{
          validateForm: validateForm,
        }"
          buttonType="primary"
          shape="round"
          [iconType]="'save'"
          (nzOnOkClick)="submitForm()"
        >
          儲存商品
        </lib-modal>
        <lib-modal
          description="是否刪除此商品?"
          [danger]="true"
          shape="round"
          [iconType]="'delete'"
          (nzOnOkClick)="handleDeleteClick()"
        >
          刪除商品
        </lib-modal>
      </div>
    </nz-form-control>
  </nz-form-item>
</form>
