import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ButtonComponent } from '@lib/button/button.component';

@Component({
  imports: [ButtonComponent, RouterModule],
  selector: 'app-root',
  template: `
    <lib-button label="Click me" buttonType="primary" shape="round" iconType="edit"></lib-button>
    <router-outlet></router-outlet>
  `,
  standalone: true,
})
export class AppComponent {
  title = 'pilot-logo-Admin';
}
