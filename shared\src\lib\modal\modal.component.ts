import {
  Component,
  Input,
  Output,
  EventEmitter,
  TemplateRef,
  Type,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup } from '@angular/forms';

import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';

import { User } from '../../../src/models/user.model';
import { Contact } from '../../../src/models/contact.model';
import { ButtonComponent } from '../button/button.component';
@Component({
  selector: 'lib-modal',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  templateUrl: './modal.component.html',
  styles: [
    `
      button {
        margin-right: 8px;
      }
    `,
  ],
  providers: [
    NzModalService
  ]
})
export class ModalComponent {
  @Input() shape: 'circle' | 'round' | null = 'circle';
  @Input() iconType = 'delete';
  @Input() danger = false;
  @Input() description: string | TemplateRef<any> = '';
  @Input() actionType: 'simple' | 'dynamic' | 'editUser' | 'editContact' = 'simple';
  @Input() buttonType: 'default' | 'primary' | 'dashed' | 'link' = 'primary';
  @Input() enabled = true;
  @Input() width = '416px';

  @Input() user: User = new User();
  @Input() contact: Contact = new Contact();

  @Output() formSubmit = new EventEmitter<any>();
  @Output() nzOnOkClick = new EventEmitter<void>();
  @Input() modalConfig: {
    title?: string;
    content?: Type<any>;
    okText?: string;
    width?: string;
    validateForm?: FormGroup;
  } = {
    title: '',
    content: undefined,
    okText: '確定',
  };

  constructor(
    private modal: NzModalService,
    private message: NzMessageService
  ) {}

  showConfirm(): void {
    if (this.modalConfig.validateForm && !this.modalConfig.validateForm.valid) {
      this.message.error('請確認所有必填欄位!');
      // 表單驗證失敗，標記所有控件為已觸碰
      Object.values(this.modalConfig.validateForm.controls).forEach(
        (control: any) => {
          if (control.invalid) {
            control.markAsDirty();
            control.updateValueAndValidity({ onlySelf: true });
          }
        }
      );
      return;
    }
    switch (this.actionType) {
      case 'simple':
        this.showSimpleConfirm();
        break;
      default:
        this.showDynamicForm();
        break;
    }
  }

  showSimpleConfirm(): void {
    this.modal.confirm({
      nzTitle: '請確認',
      nzContent: this.description,
      nzOkText: '是',
      nzOkType: 'primary',
      nzOkDanger: this.danger,
      nzWidth: this.width,
      nzOnOk: () => this.nzOnOkClick.emit(),
      nzCancelText: '取消',
      nzOnCancel: () => console.log('取消'),
    });
  }

  showDynamicForm(): void {
    const modal = this.modal.create({
      nzTitle: this.modalConfig.title,
      nzContent: this.modalConfig.content,
      nzOkText: this.modalConfig.okText || null,
      nzOkType: 'primary',
      nzCancelText: this.modalConfig.okText ? '取消' : null,
      nzStyle: this.modalConfig.width ? { width: this.modalConfig.width } : {},
      nzOnOk: () => {
        const instance = modal.getContentComponent();
        // 執行表單提交(這裡先不驗證，各Component自行驗證)
        instance.submitForm();
        // 檢查表單是否有效否則不關閉Modal
        if (instance.validateForm && instance.validateForm.valid) {
          return true;
        }
        return false;
      },
    });

    const instance = modal.getContentComponent();
    // 添加空值檢查
    if (instance && instance.formSubmit) {
      instance.formSubmit.subscribe((result: unknown) => {
        this.formSubmit.emit(result);
        this.modal.closeAll();
      });
    }

    // 傳遞必要的數據給模態框內容
    if (instance) {
      switch (this.actionType) {
        case 'editUser':
          instance.user = this.user;
          break;
        case 'editContact':
          instance.contact = this.contact;
          break;
      }
    }
  }
}
