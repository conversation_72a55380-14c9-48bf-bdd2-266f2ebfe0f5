import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { FormsModule } from '@angular/forms';
@Component({
  selector: 'lib-input-number',
  standalone: true,
  imports: [CommonModule, NzInputNumberModule, FormsModule],
  templateUrl: './input-number.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: InputNumberComponent,
      multi: true,
    },
  ],
})
export class InputNumberComponent implements ControlValueAccessor {
  @Input() value = 1;
  @Input() min = 1;
  @Input() max = 100;
  @Input() step = 1;
  @Input() size: 'large' | 'default' | 'small' = 'large';
  @Output() valueChange = new EventEmitter<number>();

  onChange: any = () => {
    /* empty */
  };
  onTouched: any = () => {
    /* empty */
  };

  writeValue(value: number): void {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
}
