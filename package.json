{"name": "@admin-angular/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"@angular-devkit/architect": "^0.1900.7", "@angular/animations": "~19.0.0", "@angular/cdk": "^19.2.17", "@angular/common": "~19.0.0", "@angular/compiler": "~19.0.0", "@angular/core": "~19.0.0", "@angular/forms": "~19.0.0", "@angular/platform-browser": "~19.0.0", "@angular/platform-browser-dynamic": "~19.0.0", "@angular/platform-server": "~19.0.0", "@angular/router": "~19.0.0", "@angular/ssr": "~19.0.0", "@types/quill": "1.3.10", "express": "^4.21.2", "less": "^4.2.1", "less-loader": "^12.2.0", "ng-zorro-antd": "^19.0.2", "ngx-quill": "^27.1.2", "quill": "^2.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "rxjs": "~7.8.0", "tinycolor2": "^1.6.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "~19.0.0", "@angular-devkit/core": "~19.0.0", "@angular-devkit/schematics": "~19.0.0", "@angular/cli": "~19.0.0", "@angular/compiler-cli": "~19.0.0", "@angular/language-service": "~19.0.0", "@eslint/js": "^9.8.0", "@nx/angular": "20.5.0", "@nx/eslint": "20.5.0", "@nx/eslint-plugin": "20.5.0", "@nx/jest": "20.5.0", "@nx/js": "20.5.0", "@nx/storybook": "20.1.4", "@nx/web": "20.5.0", "@nx/workspace": "20.5.0", "@schematics/angular": "~19.0.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/express": "4.17.14", "@types/jest": "^29.5.12", "@types/node": "18.16.9", "@typescript-eslint/utils": "^8.13.0", "angular-eslint": "^19.0.2", "autoprefixer": "^10.4.0", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-preset-angular": "~14.4.0", "jsonc-eslint-parser": "^2.1.0", "ng-packagr": "^19.0.0", "nx": "20.5.0", "postcss": "^8.4.5", "postcss-url": "~10.1.3", "prettier": "^2.6.2", "tailwindcss": "^3.0.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.6.2", "typescript-eslint": "^8.13.0"}, "packageManager": "pnpm@9.15.3"}