import { Component } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms';
import { LoginService } from '../../services/login.service';
import { Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ReactiveFormsModule } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
@Component({
  selector: 'lib-login',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NzFormModule,
    NzSpaceModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent {
  validateForm: FormGroup<{
    account: FormControl<string>;
    password: FormControl<string>;
    remember: FormControl<boolean>;
  }>;
  passwordVisible = false;
  constructor(private loginService: LoginService, private router: Router, private fb: NonNullableFormBuilder, private message: NzMessageService) {
    this.validateForm = this.fb.group({
      account: ['', [Validators.required]],
      password: ['', [Validators.required]],
      remember: [true]
    });
  }

  error: string | undefined;

  submitForm(): void {
    if (this.validateForm.valid) {
      const { account, password } = this.validateForm.value;
      this.loginService.login(account as string, password as string).subscribe({
        next: () => {
          this.router.navigate(['/home']).then(() => {
            window.location.reload();
          });
        },
        error: (err) => {
          this.message.create('error', '登入失敗!' + err.error.message);
        } 
      });
    } else {  
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
}
