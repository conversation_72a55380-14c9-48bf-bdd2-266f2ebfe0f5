<form nz-form [formGroup]="validateForm" [nzLayout]="'horizontal'">
  <nz-form-item>
    <nz-form-label [nzSpan]="24" [nzLg]="4" nzRequired nzFor="categoryName">
      分類名稱
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20">
      <lib-input [formControl]="validateForm.controls.categoryName" />
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-label
      [nzSpan]="24"
      [nzLg]="4"
      nzRequired
      nzFor="imagePath"
      nzTooltipTitle="欲上傳照片，請先刪除現有照片"
    >
      商品分類圖片
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20">
      <lib-upload-image
        [fileList]="fileList"
        uploadFolder="category"
        (uploadEvent)="uploadImage($event)"
      />
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-control [nzOffset]="0" [nzSpan]="24" [nzLg]="20">
      <div nz-flex [nzGap]="30">
        <lib-modal
          [modalConfig]="{
          validateForm: validateForm,
        }"
          buttonType="primary"
          shape="round"
          [iconType]="'save'"
          (nzOnOkClick)="submitForm()"
          [description]="'是否要儲存此商品分類？'"
        >
          儲存
        </lib-modal>
        <lib-modal
          [enabled]="enabledDelete"
          [danger]="true"
          shape="round"
          [iconType]="'delete'"
          (nzOnOkClick)="handleDeleteClick()"
          [description]="description"
          nz-tooltip
          nzTooltipTitle="請確保此分類的商品數為0"
        >
          刪除
        </lib-modal>
        <ng-template #description>
          <p>
            是否要刪除 <b>{{ validateForm.controls.categoryName.value }} </b> ?
          </p>
          <p class="text-rose-500 font-medium">※請確保此分類的商品數為0</p>
        </ng-template>
      </div>
    </nz-form-control>
  </nz-form-item>
</form>
