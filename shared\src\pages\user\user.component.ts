import { Component, OnInit } from '@angular/core';
import { User } from '../../models/user.model';
import { UserService } from '../../services/user.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PermissionCheckService } from '../../services/permission-check.service';
import { RegisterComponent } from './register/register.component';
import { Role, RoleSelectService } from '../../services/role-select.service';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { DropdownMenuSearchComponent } from '@lib/dropdown-menu-search/dropdown-menu-search.component';
import { TagComponent } from '@lib/tag/tag.component';
import { ModalComponent } from '@lib/modal/modal.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { DatePipe } from '@angular/common';
@Component({
  selector: 'lib-user',
  templateUrl: './user.component.html',
  imports: [
    ModalComponent,
    NzTableModule,
    NzFlexModule,
    NzDropDownModule,
    DropdownMenuSearchComponent,
    TagComponent,
    DatePipe,
  ]
})
export class UserComponent implements OnInit {
  constructor(private userService: UserService, private message: NzMessageService, private permissionCheckService: PermissionCheckService, private roleSelectService: RoleSelectService) { }
  RegisterComponent = RegisterComponent;
  updateUser = false;
  deleteUser = false;

  userAccount = '';
  users: User[] = [];
  filteredUsers: User[] = [];
  roles: any[] = [];
  title = '帳號管理'
  currentIndex = 0;
  listOfColumns: any[] = [];
  setCurrentIndex(index: number) {
    this.currentIndex = index;
  }

  ngOnInit(): void {
    this.loadUsers();
    this.loadPermissions();
    this.loadRoles();
    this.updateListOfColumns();
  }
  loadPermissions(): void {
    this.userAccount = this.permissionCheckService.getAccount();
    this.permissionCheckService.permissions$.subscribe({
      next: (response) => {
        this.updateUser = !!response.data.find(p => p.page === 'user')?.updateFlag;
        this.deleteUser = !!response.data.find(p => p.page === 'user')?.deleteFlag;
      },
      error: (err) => console.error('獲取權限時出錯:', err.error.message)
    });
  }

  loadUsers(): void {
    this.userService.getUsers().subscribe({
      next: (response) => {
        if (response.success) {
          this.users = response.data;
          this.filteredUsers = response.data;
        } else {
          this.message.create('error', '載入使用者資料失敗!' + response.message);
        }
      },
      error: (err) => {
        this.message.create('error', '載入使用者資料失敗!' + err.error.message);
      }
    });
  }

  loadRoles(): void {
    this.roleSelectService.getRoles().subscribe(roles => {
      this.roles = roles.data.map((role: any) => ({
        text: role.roleName,
        value: role.roleName,
      }));
      this.updateListOfColumns();
    });
  }

  handleAddClick(formValue: any): void {
    this.userService.addUser(formValue).subscribe({
      next: () => {
        this.message.create('success', '新增帳號成功!');
        this.loadUsers();
      },
      error: (err) => {
        this.message.create('error', '新增帳號失敗!' + (err.error?.message || err.message));
      }
    });
  }

  handleDeleteClick(account: string): void {
    this.userService.deleteUsers(account).subscribe({
      next: () => {
        this.message.create('success', '刪除帳號成功!');
        this.loadUsers();
      },
      error: (err) => {
        this.message.create('error', '刪除帳號失敗!' + err.error.message);
      }
    });
  }

  handleEditClick(account: string, formValue: any): void {
    this.userService.updateUser(account, formValue).subscribe({
      next: () => {
        this.message.create('success', '更新帳號成功!');
        this.loadUsers();
      },
      error: (err) => {
        this.message.create('error', '更新帳號失敗!' + err.error.message);
      }
    });
  }

  newTable(event: any) {
    this.filteredUsers = event;
  }

  updateListOfColumns(): void {
    this.listOfColumns = [
      {
        title: '操作',
      },
      {
        title: '使用者帳號',
        compare: (a: User, b: User) => a.account.localeCompare(b.account),
        searchValue: '',
        visible: false,
        key: 'account',
      },
      {
        title: '使用者名稱',
        compare: (a: User, b: User) => a.userName.localeCompare(b.userName),
        searchValue: '',
        visible: false,
        key: 'userName',
      },
      {
        title: '系統角色',
        compare: (a: User, b: User) => a.roleName.localeCompare(b.roleName),
        listOfFilter: this.roles,
        filterFn: (list: string[], item: Role) => list.some(name => item.roleName.indexOf(name) !== -1),
        key: 'roleName',
      },
      {
        title: '上次登入時間',
      },
      {
        title: '修改時間',
      },
      {
        title: '啟用否',
      },
    ];
  }
}

