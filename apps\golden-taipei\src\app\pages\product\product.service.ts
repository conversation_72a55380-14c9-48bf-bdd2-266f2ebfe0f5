import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { Product } from '@models/product.model';
import { ApiResponse } from '@models/api-response.model';
@Injectable({
  providedIn: 'root',
})
export class ProductService {
  constructor(private http: HttpClient) {}

  private apiUrl = environment.apiUrl + '/product';

  getProducts(productId?: string, categoryId?: number) {
    let url = this.apiUrl;
    if (productId) url += `?productId=${productId}`;
    if (categoryId) url += `${productId ? '&' : '?'}categoryId=${categoryId}`;
    return this.http.get<ApiResponse<Product[]>>(url);
  }

  deleteProduct(productId: string) {
    return this.http.delete<ApiResponse<Product>>(
      `${this.apiUrl}/DeleteProduct/${productId}`
    );
  }

  createProduct(product: any) {
    return this.http.post<ApiResponse<Product>>(
      this.apiUrl + '/CreateProduct',
      product
    );
  }

  updateProduct(product: any) {
    return this.http.put<ApiResponse<Product>>(
      this.apiUrl + '/UpdateProduct',
      product
    );
  }
}
