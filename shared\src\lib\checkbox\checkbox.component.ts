import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { FormsModule } from '@angular/forms';
@Component({
  selector: 'lib-checkbox',
  standalone: true,
  imports: [CommonModule, NzCheckboxModule, FormsModule],
  template: `
    <label
      nz-checkbox
      [(ngModel)]="checked"
      (ngModelChange)="onCheckedChange($event)"
    ></label>
  `,
})
export class CheckboxComponent {
  @Input() checked = false;
  @Output() checkedChange = new EventEmitter<boolean>();

  onCheckedChange(value: boolean) {
    this.checkedChange.emit(value);
  }
}
