{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@admin-angular/shared-library": ["shared/src/index.ts"], "@lib/*": ["shared/src/lib/*"], "@models/*": ["shared/src/models/*"], "@pages/*": ["shared/src/pages/*"], "@services/*": ["shared/src/services/*"]}}, "exclude": ["node_modules", "tmp"]}