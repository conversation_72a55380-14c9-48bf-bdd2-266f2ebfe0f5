import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { User } from '../models/user.model';
import { ApiResponse } from '../models/api-response.model';
@Injectable({
  providedIn: 'root'
})
export class UserService {
  private apiUrl: string;

  constructor(private http: HttpClient, @Inject('API_URL') private baseApiUrl: string) {
    this.apiUrl = `${this.baseApiUrl}/User`;
  }

  getUsers(): Observable<ApiResponse<User[]>> {
    return this.http.get<ApiResponse<User[]>>(this.apiUrl + '/GetAllUsers');
  }

  addUser(user: User): Observable<User> {
    return this.http.post<User>(this.apiUrl + "/InsertUser", user);
  }

  deleteUsers(account: string): Observable<any> {
    return this.http.delete(this.apiUrl + "/DeleteUser/" + account);
  }

  updateUser(account: string, user: User): Observable<User> {
    return this.http.put<User>(this.apiUrl + "/UpdateUser/" + account, user);
  }

}
