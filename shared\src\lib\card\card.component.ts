import { Component, Input, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NzCardModule } from 'ng-zorro-antd/card';
import { ImageComponent } from '../image/image.component';
@Component({
  selector: 'lib-card',
  standalone: true,
  imports: [CommonModule, NzCardModule, ImageComponent],
  template: `
    <nz-card
      style="width:300px;"
      [nzCover]="defaultCoverTemplate"
      [nzHoverable]="true"
      (click)="navigateTo(link)"
    >
      <nz-card-meta
        [nzTitle]="title"
        [nzDescription]="description"
      ></nz-card-meta>
    </nz-card>
    <ng-template #defaultCoverTemplate>
      <lib-image
        [src]="image"
        [style]="style"
        [disablePreview]="disablePreview"
      />
    </ng-template>
  `,
})
export class CardComponent {
  constructor(private router: Router) {}
  @Input() coverTemplate: TemplateRef<any> | null = null;
  @Input() title = '標題';
  @Input() description = '描述';
  @Input() link = '';
  @Input() image = 'https://placehold.co/300';
  @Input() style = 'width: 300px;';
  @Input() disablePreview = true;

  navigateTo(link: string) {
    this.router.navigate([link]);
  }
}
