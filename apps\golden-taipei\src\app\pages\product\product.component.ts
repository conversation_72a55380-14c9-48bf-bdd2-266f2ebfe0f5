import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ProductService } from './product.service';
import { Product } from '@models/product.model';
import { ProductIndexComponent } from './product-index/product-index.component';
import { Subject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { CategoryService } from '../category/category.service';
import { Category } from '@models/category.model';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { TabsComponent } from '@lib/tabs/tabs.component';
import { ImageComponent } from '@lib/image/image.component';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
@Component({
  selector: 'app-product',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    TabsComponent,
    ImageComponent,
    NzSpinModule,
    NzPaginationModule
  ],
  templateUrl: './product.component.html',
})
export class ProductComponent implements OnInit {
  constructor(
    private productService: ProductService,
    private categoryService: CategoryService
  ) {}
  ProductEditComponent = ProductIndexComponent;
  imageUrl = environment.imageUrl + '/product/';
  products: Product[] = [];
  filteredProducts: Product[] = [];
  searchTerms$ = new Subject<string>();
  searchTerm = '';
  listOfColumns = [
    {
      title: '商品名稱',
      compare: (a: Product, b: Product) =>
        a.productName.localeCompare(b.productName),
      width: '40%',
    },
    {
      title: '商品數',
      width: '30%',
    },
  ];
  tabs: any[] = [];
  currentTabIndex = 0;
  currentTabProducts: Product[] = [];
  displayedProducts: Product[] = [];
  isLoading = false;
  // 分頁相關屬性
  currentPage = 1;
  pageSize = 24; // 每頁顯示 24 個商品 (4x6 網格)
  total = 0;

  ngOnInit() {
    this.loadProduct();
    this.loadTabs();

    // 訂閱搜尋詞變化
    this.searchTerms$.subscribe((term) => {
      this.searchTerm = term;
      this.performSearch();
    });
  }

  loadProduct() {
    this.isLoading = true;
    this.productService.getProducts().subscribe((response) => {
      this.products = response.data;
      this.currentTabProducts = this.products;
      this.total = this.products.length;
      this.updateDisplayedProducts();
      this.isLoading = false;
    });
  }

  loadTabs() {
    this.categoryService.getCategory().subscribe((response) => {
      this.tabs = response.data.map((category: Category) => ({
        name: category.categoryName,
        categoryId: category.categoryId,
      }));
      this.tabs.unshift({
        name: '全部商品',
        categoryId: undefined,
      });
    });
  }

  onTabChanged(index: number) {
    this.currentTabIndex = index;
    this.isLoading = true;
    this.currentPage = 1; // 重置到第一頁
    
    this.productService
      .getProducts(undefined, this.tabs[index].categoryId)
      .subscribe((response) => {
        this.products = response.data;
        this.currentTabProducts = response.data;
        this.total = response.data.length;
        this.performSearch();
        this.isLoading = false;
      });
  }

  performSearch() {
    if (this.searchTerm.trim()) {
      this.currentTabProducts = this.products.filter(product =>
        product.productName.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    } else {
      this.currentTabProducts = [...this.products];
    }
    this.total = this.currentTabProducts.length;
    this.currentPage = 1; // 搜尋後重置到第一頁
    this.updateDisplayedProducts();
  }

  updateDisplayedProducts() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedProducts = this.currentTabProducts.slice(startIndex, endIndex);
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.updateDisplayedProducts();
  }

  onPageSizeChange(size: number) {
    this.pageSize = size;
    this.currentPage = 1;
    this.updateDisplayedProducts();
  }

  handleAddClick(product: Product) {
    this.products.push(product);
  }

  filterProducts(): void {
    this.performSearch();
  }
}
