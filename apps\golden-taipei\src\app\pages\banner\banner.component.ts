import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import {
  DragDropModule,
  CdkDragDrop,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClientModule } from '@angular/common/http';

import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzEmptyModule } from 'ng-zorro-antd/empty';

import { Banner } from '@models/banner.model';
import { environment } from '../../../environments/environment';
import { ButtonComponent } from '@lib/button/button.component';
import { BannerService } from './banner.service';
import { ImageComponent } from '@lib/image/image.component';
import { ModalComponent } from '@lib/modal/modal.component';
@Component({
  selector: 'app-banner',
  standalone: true,
  imports: [
    CommonModule,
    NzTableModule,
    DragDropModule,
    NzIconModule,
    ButtonComponent,
    RouterModule,
    ImageComponent,
    ModalComponent,
    NzEmptyModule,
    HttpClientModule,
  ],
  templateUrl: './banner.component.html',
  styleUrl: './banner.component.scss',
})
export class BannerComponent implements OnInit {
  constructor(
    private bannerService: BannerService,
    private message: NzMessageService
  ) {}
  banners$: Observable<any> | undefined;
  banners: Banner[] = [];
  imageUrl = environment.imageUrl;
  listOfColumns = [
    {
      title: '排序',
      width: '5%',
    },
    {
      title: '操作',
      width: '10%',
    },
    {
      title: '廣告橫幅',
      compare: (a: Banner, b: Banner) => a.bannerId - b.bannerId,
      width: '40%',
    },
    {
      title: '連結',
      compare: (a: Banner, b: Banner) => a.link.localeCompare(b.link),
      width: '30%',
    },
    {
      title: '啟用否',
      compare: (a: Banner, b: Banner) =>
        a.enable ? 1 : 0 - (b.enable ? 1 : 0),
      width: '10%',
    },
  ];

  ngOnInit(): void {
    this.bannerService
      .getBanner()
      .pipe(map((response) => response.data))
      .subscribe((data) => {
        this.banners = data;
      });
  }

  drop(event: CdkDragDrop<any[]>): void {
    moveItemInArray(this.banners, event.previousIndex, event.currentIndex);
    this.banners.forEach((banner) => {
      banner.sort = this.banners.indexOf(banner);
    });
    console.log(this.banners);
    // 如果需要將新順序保存到後端，可以在這裡調用 API
    this.bannerService.updateSort(this.banners).subscribe((response) => {
      if (response.success) {
        this.message.success('更新廣告橫幅排序成功');
        this.ngOnInit();
      }
    });
  }

  handleDeleteClick(bannerId: number): void {
    this.bannerService
      .deleteBanner(bannerId.toString())
      .subscribe((response) => {
        if (response.success) {
          this.message.success('刪除廣告橫幅資料成功');
          this.ngOnInit();
        }
      });
  }
}
