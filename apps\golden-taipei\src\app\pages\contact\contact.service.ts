import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiResponse } from '@models/api-response.model';
import { Contact } from '@models/contact.model';
@Injectable({
  providedIn: 'root',
})
export class ContactService {
  constructor(private http: HttpClient) {}
  private apiUrl = environment.apiUrl + '/contact';

  getContact(): Observable<ApiResponse<Contact[]>> {
    return this.http.get<ApiResponse<Contact[]>>(this.apiUrl);
  }

  createContact(contact: Contact): Observable<ApiResponse<Contact>> {
    return this.http.post<ApiResponse<Contact>>(this.apiUrl, contact);
  }

  updateContact(contact: Contact): Observable<ApiResponse<Contact>> {
    return this.http.put<ApiResponse<Contact>>(this.apiUrl, contact);
  }

  deleteContact(id: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`);
  }
}
