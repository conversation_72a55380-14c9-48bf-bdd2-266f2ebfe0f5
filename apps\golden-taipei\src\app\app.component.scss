:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  min-height: 100vh;
}

.menu-sidebar {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
}

.header-trigger {
  height: 64px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s, padding 0s;
}

.trigger:hover {
  color: #1890ff;
}

.sidebar-logo {
  position: relative;
  height: 64px;
  padding-left: 24px;
  overflow: hidden;
  line-height: 64px;
  background: #001529;
  transition: all 0.3s;
}

.sidebar-logo img {
  display: inline-block;
  height: 32px;
  width: 32px;
  vertical-align: middle;
}

.sidebar-logo h1 {
  display: inline-block;
  margin: 0 0 0 20px;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
  font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
  vertical-align: middle;
}

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

nz-content {
  margin: 24px 16px 0;
  overflow: initial;
  /* flex: 1;
  display: flex;
  flex-direction: column; */
}

.inner-content {
  padding: 24px;
  // background: #fff;
  // text-align: center;
  height: 100%;
  // min-height: 100%;
}

.logo {
  max-width: 100%; /* 根据需要調整 logo 大小 */
}

#user {
  position: absolute;
  right: 0;
  top: 0;
  margin-right: 30px;
}

nz-sider {
  overflow: auto;
  height: 100%;
  position: fixed;
  left: 0;
}

.right-layout {
  transition: margin-left 0.3s;
  margin-left: 256px;
}

nz-footer {
  text-align: center;
}

.right-layout.collapsed {
  margin-left: 0;
}

/* :host ::ng-deep .ant-breadcrumb {
  text-align: left !important;
} */

.notification-icon {
  display: inline-block;
  vertical-align: middle;
}
