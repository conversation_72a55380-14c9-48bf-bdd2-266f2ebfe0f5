{"name": "tcb-ebank", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/tcb-ebank/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/tcb-ebank", "index": "apps/tcb-ebank/src/index.html", "browser": "apps/tcb-ebank/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/tcb-ebank/tsconfig.app.json", "inlineStyleLanguage": "less", "assets": [{"glob": "**/*", "input": "apps/tcb-ebank/public"}, {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["apps/tcb-ebank/src/styles.less"], "stylePreprocessorOptions": {"includePaths": ["shared/src/styles/themes"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "tcb-ebank:build:production"}, "development": {"buildTarget": "tcb-ebank:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "tcb-ebank:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/tcb-ebank/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "tcb-ebank:build", "staticFilePath": "dist/apps/tcb-ebank/browser", "spa": true}}}}