@if (showSidebar) {
<nz-layout class="app-layout">
  <nz-sider
    class="menu-sidebar"
    nzCollapsible
    nzWidth="256px"
    nzBreakpoint="lg"
    [nzCollapsedWidth]="0"
    [(nzCollapsed)]="isCollapsed"
    [style.position]="'fixed'"
    [style.left]="0"
    [style.top]="0"
    [style.z-index]="1"
  >
    <div class="sidebar-logo">
      <a routerLink="/home">
        <img src="./favicon.ico" alt="logo" class="logo" />
        <h1>金台北批發管理系統</h1>
      </a>
    </div>
    <ul
      nz-menu
      nzTheme="dark"
      nzMode="inline"
      [nzInlineCollapsed]="isCollapsed"
    >
      <!-- 首頁 -->
      <li nz-menu-item nz-tooltip nzMatchRouter>
        <span nz-icon nzType="home"></span>
        <span>首頁</span>
        <a routerLink="/home"> </a>
      </li>
      <!-- 商家管理 -->
      @if (permissionFlags$ | async; as permissionFlags) { @if
      (permissionFlags['merchant'].readFlag) {
      <li nz-menu-item nz-tooltip nzMatchRouter>
        <span nz-icon nzType="shop"></span>
        <span>商家管理</span>
        <a routerLink="/merchant"> </a>
      </li>
      }
      <!-- 商品分類管理 -->
      @if (permissionFlags['category'].readFlag) {
      <li nz-menu-item nz-tooltip nzMatchRouter>
        <span nz-icon nzType="shopping"></span>
        <span>商品分類管理</span>
        <a routerLink="/category"> </a>
      </li>
      }
      <!-- 商品管理 -->
      @if (permissionFlags['product'].readFlag) {
      <li nz-menu-item nz-tooltip nzMatchRouter>
        <span nz-icon nzType="dollar"></span>
        <span>商品管理</span>
        <a routerLink="/product"> </a>
      </li>
      }
      <!-- 廣告橫幅管理 -->
      @if (permissionFlags['banner'].readFlag) {
      <li nz-menu-item nz-tooltip nzMatchRouter>
        <span nz-icon nzType="like"></span>
        <span>廣告橫幅管理</span>
        <a routerLink="/banner"> </a>
      </li>
      }
      <!-- 聯絡方式管理 -->
      @if (permissionFlags['contact'].readFlag) {
      <li nz-menu-item nz-tooltip nzMatchRouter>
        <span nz-icon nzType="phone"></span>
        <span>聯絡方式管理</span>
        <a routerLink="/contact"> </a>
      </li>
      }
      <!-- 系統管理 -->
      @if (permissionFlags['permission'].readFlag ||
      permissionFlags['user'].readFlag || permissionFlags['log'].readFlag) {
      <li nz-submenu nzTitle="系統管理" nzIcon="user">
        <ul>
          @if (permissionFlags['permission'].readFlag) {
          <li nz-menu-item nzMatchRouter>
            <a routerLink="/permission">權限維護管理</a>
          </li>
          } @if (permissionFlags['user'].readFlag) {
          <li nz-menu-item nzMatchRouter>
            <a routerLink="/user">帳號維護管理</a>
          </li>
          } @if (permissionFlags['log'].readFlag) {
          <li nz-menu-item nzMatchRouter>
            <a routerLink="/log">系統日誌</a>
          </li>
          }
        </ul>
      </li>
      } }
    </ul>
  </nz-sider>
  <nz-layout class="right-layout" [class.collapsed]="isCollapsed">
    <nz-header>
      <div class="app-header">
        <span
          class="header-trigger"
          (click)="isCollapsed = !isCollapsed"
          (keydown.enter)="isCollapsed = !isCollapsed"
          tabindex="0"
          role="button"
          aria-label="切換選單"
        >
          <span
            class="trigger"
            nz-icon
            [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'"
          ></span>
        </span>
        <!-- <span class="header-trigger" (click)="toggleTheme()">
          <i class="trigger" nz-icon nzType="skin" nzTheme="outline"></i>
        </span> -->
        <div nz-flex [nzGap]="30" id="user">
          <!-- <a routerLink="/home">
            <nz-badge [nzCount]="contentReviewCount">
              <div class="notification-icon">
                <a nz-icon nzType="notification"> </a>
              </div>
            </nz-badge>
          </a> -->
          <lib-avatar
            nz-dropdown
            [nzDropdownMenu]="menu"
            id="avatar"
            [text]="username.substring(0, 1)"
          ></lib-avatar>
          <nz-dropdown-menu #menu="nzDropdownMenu">
            <ul nz-menu>
              <li nz-menu-group [nzTitle]="username"></li>
              <li nz-menu-item [nzDisabled]="true">個人資料</li>
              <li nz-menu-item [nzDisabled]="true">設置</li>
              <li nz-menu-divider></li>
              <li
                nz-menu-item
                nzButtonType="default"
                [nzDanger]="true"
                (click)="handleLogoutClick()"
                (keydown.enter)="handleLogoutClick()"
                role="menuitem"
                tabindex="0"
                aria-label="登出系統"
              >
                登出
              </li>
            </ul>
          </nz-dropdown-menu>
        </div>
      </div>
    </nz-header>
    <nz-content>
      <nz-breadcrumb [nzAutoGenerate]="true">
        <nz-breadcrumb-item>
          <a routerLink="/home">首頁</a>
        </nz-breadcrumb-item>
      </nz-breadcrumb>
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
    <nz-footer
      >Singho Digital Marketing ©{{ currentYear }} Implement By
      Angular</nz-footer
    >
  </nz-layout>
</nz-layout>
} @else {
<router-outlet></router-outlet>
}
