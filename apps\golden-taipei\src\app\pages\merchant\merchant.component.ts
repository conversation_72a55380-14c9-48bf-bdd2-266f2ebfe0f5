import { Component, OnInit } from '@angular/core';
import { MerchantService } from './merchant.service';
import { Observable, BehaviorSubject, combineLatest } from 'rxjs';
import { Merchant } from '@models/merchant.model';
import { map, startWith, distinctUntilChanged } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { RouterModule } from '@angular/router';
import { ImageComponent } from '@lib/image/image.component';
import { FormsModule } from '@angular/forms';
import { AsyncPipe } from '@angular/common';
@Component({
  templateUrl: './merchant.component.html',
  imports: [RouterModule, ImageComponent, FormsModule, AsyncPipe],
})
export class MerchantComponent implements OnInit {
  constructor(private merchantService: MerchantService) {}
  imageUrl = environment.imageUrl;
  merchants$: Observable<Merchant[]> | undefined;
  searchTerm = '';
  private searchTerms = new BehaviorSubject<string>('');
  filteredMerchants$: Observable<Merchant[]> | undefined;

  ngOnInit(): void {
    this.merchants$ = this.merchantService
      .getMerchant()
      .pipe(map((response) => response.data));

    this.filteredMerchants$ = combineLatest([
      this.merchants$,
      this.searchTerms.pipe(
        startWith(''),
        // debounceTime(300),
        distinctUntilChanged()
      ),
    ]).pipe(
      map(([merchants, term]) =>
        merchants.filter((merchant) =>
          merchant.merchantName.toLowerCase().includes(term.toLowerCase())
        )
      )
    );
  }

  filterMerchants(): void {
    this.searchTerms.next(this.searchTerm);
  }
}
