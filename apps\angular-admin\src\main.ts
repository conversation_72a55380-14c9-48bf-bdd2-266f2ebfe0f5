import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { AppInitializerProvider } from '../../../shared/src/services/app-initializer.service';
import { AuthInterceptor } from '../../../shared/src/services/auth.interceptor';
import { withInterceptors } from '@angular/common/http';
import { NZ_I18N } from 'ng-zorro-antd/i18n';
import { zh_TW } from 'ng-zorro-antd/i18n';
import { LOCALE_ID } from '@angular/core';
import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';
import { MockInterceptor } from './app/mock.interceptor';
registerLocaleData(zh);

bootstrapApplication(AppComponent, {
  ...appConfig,
  providers: [
    ...(appConfig.providers || []),
    ...AppInitializerProvider.initializers,
    { provide: 'API_URL', useValue: 'http://localhost:4200' },
    { provide: LOCALE_ID, useValue: 'zh-TW' },
    { provide: NZ_I18N, useValue: zh_TW },
    provideHttpClient(withInterceptors([MockInterceptor, AuthInterceptor])),
    provideAnimations(),
  ],
}).catch((err) => console.error(err));
