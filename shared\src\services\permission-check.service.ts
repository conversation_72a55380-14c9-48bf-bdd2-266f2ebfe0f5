import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { ApiResponse } from '../models/api-response.model';
import { Permission } from '../models/permission.model';

export interface PermissionFlags {
  readFlag: boolean;
  updateFlag: boolean;
  createFlag: boolean;
  deleteFlag: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class PermissionCheckService {
  private apiUrl: string;
  permissions$: Observable<ApiResponse<Permission[]>>;
  username = '';
  account = '';
  role = 9;

  constructor(
    private http: HttpClient,
    @Inject('API_URL') private baseApiUrl: string
  ) {
    this.apiUrl = `${this.baseApiUrl}/Permission/GetRolePermission`;
    this.permissions$ = this.http.get<ApiResponse<Permission[]>>(this.apiUrl);
    this.initializeUserInfo();
  }

  private initializeUserInfo() {
    const token = localStorage.getItem('token');
    if (token) {
      const jwt = this.parseJwt(token);
      if (jwt) {
        this.account = jwt.account;
        this.username = jwt.userName;
        this.role = jwt.role;
      }
    }
  }
  
  parseJwt(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      return JSON.parse(jsonPayload);
    } catch (e) {
      return null;
    }
  };

  getAccount(): string {
    return this.account;
  }

  getUsername(): string {
    return this.username;
  }

  getRole(): number {
    return this.role;
  }

  checkPermission(): Observable<{ [key: string]: PermissionFlags; }> {
    return this.permissions$.pipe(
      map(response => {
        const permissionFlags: { [key: string]: PermissionFlags } = {};
        if (response && response.data) {
          response.data.forEach((permission) => {
          if (permission) {
            permissionFlags[permission.page] = {
              readFlag  : permission.readFlag   || false,
              updateFlag: permission.updateFlag || false,
              createFlag: permission.createFlag || false,
              deleteFlag: permission.deleteFlag || false,
            };
          }
          });
        }
        return permissionFlags;
      })
    );
  }
}