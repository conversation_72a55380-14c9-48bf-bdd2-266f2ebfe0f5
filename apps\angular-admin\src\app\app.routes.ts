import { Route } from '@angular/router';
import { LoginComponent } from '@pages/login/login.component';
import { LogComponent } from '@pages/log/log.component';
import { PermissionComponent } from '@pages/permission/permission.component';
import { UserComponent } from '@pages/user/user.component';
import { HomeComponent } from '@pages/home/<USER>';
import { InputComponent } from './pages/input/input.component';
export const appRoutes: Route[] = [
  { path: 'login', component: LoginComponent },
  {
    path: 'input',
    component: InputComponent,
    data: { breadcrumb: 'Input' },
  },
  {
    path: 'log',
    component: LogComponent,
    data: { breadcrumb: '系統日誌' },
  },
  {
    path: 'permission',
    component: PermissionComponent,
    data: { breadcrumb: '權限管理' },
  },
  {
    path: 'user',
    component: UserComponent,
    data: { breadcrumb: '使用者管理' },
  },
  { path: 'home', component: HomeComponent },

  { path: '**', redirectTo: 'home' },
];
