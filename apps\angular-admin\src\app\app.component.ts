import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd, RouterModule } from '@angular/router';
import {
  PermissionCheckService,
  PermissionFlags,
} from 'shared/src/services/permission-check.service';
import { Observable } from 'rxjs';

import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { AvatarComponent } from '../../../../shared/src/lib/avatar/avatar.component';
@Component({
  standalone: true,
  imports: [
    RouterModule,
    NzLayoutModule,
    NzMenuModule,
    NzIconModule,
    NzButtonModule,
    NzBadgeModule,
    NzFlexModule,
    AvatarComponent,
    NzBreadCrumbModule,
    NzDropDownModule,
  ],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  providers: [{ provide: 'API_URL', useValue: 'http://localhost:3000' }],
})
export class AppComponent implements OnInit {
  isCollapsed = false;
  showSidebar = true;
  permissionFlags$: Observable<{ [key: string]: PermissionFlags }> | undefined;
  username = '';
  contentReviewCount = 0;
  currentYear = new Date().getFullYear();

  constructor(
    private router: Router,
    // private permissionCheckService: PermissionCheckService
  ) {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.showSidebar = !this.router.url.startsWith('/login');
        if (window.innerWidth <= 992) {
          this.isCollapsed = true;
        }
      }
    });
  }

  ngOnInit() {
    // if (!localStorage.getItem('token')) {
    //   this.router.navigate(['/login']);
    // } else {
    //   this.username = this.permissionCheckService.getUsername();
    //   this.permissionFlags$ = this.permissionCheckService.checkPermission();
    // }
  }

  handleLogoutClick() {
    localStorage.removeItem('token');
    this.router.navigate(['/login']);
  }

  // toggleTheme(): void {
  //   this.themeService.toggleTheme().then();
  // }
}
