import { Component, OnInit } from '@angular/core';
import { Permission } from '../../models/permission.model';
import { PermissionService } from '../../services/permission.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PermissionCheckService } from '../../services/permission-check.service';
import { RoleSelectService } from '../../services/role-select.service';
import { ButtonComponent } from '@lib/button/button.component';
import { TagComponent } from '@lib/tag/tag.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { InputComponent } from '@lib/input/input.component';
import { CheckboxComponent } from '@lib/checkbox/checkbox.component';
import { ModalComponent } from '@lib/modal/modal.component';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { FormsModule } from '@angular/forms';
@Component({
  selector: 'lib-permission',
  templateUrl: './permission.component.html',
  // styleUrls: ['../table.component.css'],
  standalone: true,
  imports: [
    ButtonComponent,
    TagComponent,
    NzTableModule,
    InputComponent,
    CheckboxComponent,
    ModalComponent,
    NzFlexModule,
    FormsModule
  ]
})
export class PermissionComponent implements OnInit {
  roles: any[] = [];
  listOfColumns: any[] = [];
  title = '權限管理';

  constructor(private permissionService: PermissionService, 
              private message: NzMessageService, 
              private permissionCheckService: PermissionCheckService, 
              private roleSelectService: RoleSelectService) {}

  ngOnInit(): void {
    this.loadAllPermissions();
    this.loadPermissions();
    this.loadRoles();
  }

  loadRoles(): void {
    this.roleSelectService.getRoles().subscribe({
      next: (response) => {
        this.roles = response.data.map((role: any) => ({
          text: role.roleName,
          value: role.roleName
        }));
        this.updateListOfColumns();
      },
      error: (err) => {
        this.message.create('error', '載入系統角色資料失敗!' + err.error.message);
      }
    });
  }

  updateListOfColumns(): void {
    this.listOfColumns = [
      {
        title: '操作',
      },
      {
        title: '系統角色',
        compare: (a: Permission, b: Permission) => a.roleName.localeCompare(b.roleName),
        filterFn: (list: string[], item: Permission) => list.some(name => item.roleName.indexOf(name) !== -1),
        listOfFilter: this.roles,
        key: 'roleName',
      },
      {
        title: '後台頁面',
        compare: (a: Permission, b: Permission) => a.pageName.localeCompare(b.pageName),
      },
      {
        title: '查看否',
      },
      {
        title: '新增否',
      },
      {
        title: '修改否',
      },
      {
        title: '刪除否',
      },
    ];
  }

  permissions: Permission[] = [];
  isEditing: boolean[] = [];
  updatePermission = false;
  deletePermission = false;

  loadPermissions(): void {
    this.permissionCheckService.permissions$.subscribe({
      next: (response) => {
        this.updatePermission = !!response.data.find(p => p.page === 'permission')?.updateFlag;
        this.deletePermission = !!response.data.find(p => p.page === 'permission')?.deleteFlag;
      },
      error: (err) => console.error('獲取權限時出錯:', err.error.message)
    });
  }

  loadAllPermissions(): void {
    this.permissionService.getPermissions().subscribe({
      next: (response) => {
        this.permissions = response.data;
      },
      error: (err) => {
        this.message.create('error', '載入權限資料失敗!' + err.error.message);
      }
    });
  }

  handleEditClick(index: number): void {
    this.isEditing[index] = !this.isEditing[index];
  }

  handleEditOkClick(index: number) {
    const permission = this.permissions[index];
    console.log(permission);
    this.permissionService.updatePermission(permission).subscribe({
      next: () => {
        this.message.create('success', '更新權限資料成功!');
        this.loadAllPermissions();
      },
      error: (err) => {
        this.message.create('error', '更新權限資料失敗!' + err.error.message);
        this.loadAllPermissions();
      }
    })
    this.isEditing[index] = !this.isEditing[index];
  }
}
