import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzTagModule } from 'ng-zorro-antd/tag';

@Component({
  selector: 'lib-tag',
  standalone: true,
  imports: [CommonModule, NzTagModule],
  template: `
    <nz-tag [nzColor]="color">
      <ng-content></ng-content>
    </nz-tag>
  `,
  styles: `
      .ant-tag {
        margin-bottom: 8px;
      }
  `,
})
export class TagComponent {
  @Input() color = 'blue';
}
