import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiResponse } from '@models/api-response.model';
import { Merchant } from '@models/merchant.model';
import { SortMerCat } from '@models/sort-mer-cat.model';
@Injectable({
  providedIn: 'root',
})
export class MerchantService {
  constructor(private http: HttpClient) {}
  private apiUrl = environment.apiUrl + '/Merchant';

  getMerchant(merchantId?: string): Observable<ApiResponse<Merchant[]>> {
    return this.http.get<ApiResponse<Merchant[]>>(
      this.apiUrl + (merchantId ? '?merchantId=' + merchantId : '')
    );
  }

  createMerchant(merchant: any): Observable<ApiResponse<number>> {
    return this.http.post<ApiResponse<number>>(
      this.apiUrl + '/CreateMerchant',
      merchant
    );
  }

  updateMerchant(merchant: any): Observable<ApiResponse<Merchant>> {
    return this.http.put<ApiResponse<Merchant>>(
      this.apiUrl + '/UpdateMerchant',
      merchant
    );
  }

  deleteMerchant(merchantId: string): Observable<ApiResponse<Merchant>> {
    return this.http.delete<ApiResponse<Merchant>>(
      this.apiUrl + '/DeleteMerchant/' + merchantId
    );
  }

  getSortMerCat(merchantId?: string): Observable<ApiResponse<SortMerCat[]>> {
    return this.http.get<ApiResponse<SortMerCat[]>>(
      this.apiUrl +
        '/GetSortMerCat' +
        (merchantId ? '?merchantId=' + merchantId : '')
    );
  }

  insertSortMerCat(
    sortMerCat: SortMerCat[]
  ): Observable<ApiResponse<SortMerCat[]>> {
    return this.http.post<ApiResponse<SortMerCat[]>>(
      this.apiUrl + '/InsertSortMerCat',
      sortMerCat
    );
  }
}
