import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiResponse } from '@models/api-response.model';
import { Banner } from '@models/banner.model';
@Injectable({
  providedIn: 'root',
})
export class BannerService {
  constructor(private http: HttpClient) {}
  private apiUrl = environment.apiUrl + '/banner';

  getBanner(bannerId?: string): Observable<ApiResponse<Banner[]>> {
    return this.http.get<ApiResponse<Banner[]>>(
      this.apiUrl + (bannerId ? '?bid=' + bannerId : '')
    );
  }

  createBanner(banner: any): Observable<ApiResponse<Banner>> {
    return this.http.post<ApiResponse<Banner>>(this.apiUrl, banner);
  }

  updateBanner(banner: any): Observable<ApiResponse<Banner>> {
    return this.http.put<ApiResponse<Banner>>(this.apiUrl, banner);
  }

  updateSort(banners: Banner[]): Observable<ApiResponse<Banner>> {
    return this.http.put<ApiResponse<Banner>>(
      this.apiUrl + '/UpdateSort',
      banners
    );
  }

  deleteBanner(bannerId: string): Observable<ApiResponse<Banner>> {
    return this.http.delete<ApiResponse<Banner>>(this.apiUrl + '/' + bannerId);
  }
}
