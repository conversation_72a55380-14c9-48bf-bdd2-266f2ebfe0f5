import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { RangePickerComponent } from '../range-picker/range-picker.component';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'lib-dropdown-menu-date',
  standalone: true,
  imports: [CommonModule, RangePickerComponent, NzButtonModule, FormsModule],
  templateUrl: './dropdown-menu-date.component.html',
  styleUrl: './dropdown-menu-date.component.scss',
})
export class DropdownMenuDateComponent {
  @Input() listOfColumns: any[] = [];
  @Input() dataTable: any[] = [];
  @Input() currentIndex = 0;

  @Output() filteredEvent = new EventEmitter<any[]>();

  filterDate: any;
  filteredTable: any[] = [];

  resetDate(index: number): void {
    this.filterDate = [];
    this.filteredTable = this.dataTable;
    this.listOfColumns[index].visible = false;
    this.filteredEvent.emit(this.filteredTable);
  }

  searchDate(index: number): void {
    //關閉此搜尋視窗
    this.listOfColumns[index].visible = false;
    if (this.filterDate.length == 0) {
      this.filteredTable = this.dataTable;
    } else {
      this.filteredTable = this.dataTable.filter((item: any) => {
        const columnDate = new Date(item[this.listOfColumns[index].key]);
        return (
          columnDate >= this.filterDate[0] && columnDate <= this.filterDate[1]
        );
      });
    }
    this.filteredEvent.emit(this.filteredTable);
  }
}
