import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

import { QuillComponent } from '@lib/quill/quill.component';
import { InputComponent as InputComponentLib } from '@lib/input/input.component';
import { InputNumberComponent } from '@lib/input-number/input-number.component';
import { DatePickerComponent } from '@lib/date-picker/date-picker.component';
import { RangePickerComponent } from '@lib/range-picker/range-picker.component';
import { SearchComponent } from '@lib/search/search.component';
import { ButtonComponent } from '@lib/button/button.component';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { FormControl, FormGroup, NonNullableFormBuilder, Validators, ReactiveFormsModule } from '@angular/forms';
import { NzSelectModule } from 'ng-zorro-antd/select';
@Component({
  selector: 'app-input',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    QuillComponent,
    InputComponentLib,
    InputNumberComponent,
    DatePickerComponent,
    RangePickerComponent,
    SearchComponent,
    NzCardModule,
    NzFormModule,
    NzInputModule,
    NzIconModule,
    NzButtonModule,
    NzSelectModule,
    ButtonComponent,
  ],
  templateUrl: './input.component.html'
})
export class InputComponent {
  validateForm: FormGroup<{
    password: FormControl<string>;
  }>;
  passwordVisible = false;
  constructor(private fb: NonNullableFormBuilder) {
    this.validateForm = this.fb.group({
      password: ['', [Validators.required]],
    });
    Object.values(this.validateForm.controls).forEach((control) => {
      control.markAsDirty();
    });
  }
  categories = [
    {
      categoryId: 1,
      categoryName: '蔬菜',
    },
    {
      categoryId: 2,
      categoryName: '水果',
    },
    {
      categoryId: 3,
      categoryName: '肉類',
    },
    {
      categoryId: 4,
      categoryName: '海鮮',
    },
    {
      categoryId: 5,
      categoryName: '蛋類',
    },
    {
      categoryId: 6,
      categoryName: '麵包',
    },
    {
      categoryId: 7,
      categoryName: '甜點',
    },
    
  ];
}
