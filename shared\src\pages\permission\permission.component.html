<div class="container mt-5">
  <div nz-row class="button-container">
    <h2 class="text-2xl font-semibold mb-0 flex-grow-1">{{title}}</h2>
  </div>
  <br>
  <nz-table #sortTable nzTableLayout="fixed" [nzData]="permissions" [nzPageSize]=10 [nzSize]="'small'">
    <thead>
      <tr>
        @for (column of listOfColumns; track $index) {
          <th [nzSortFn]="column.compare"
            
            [nzCustomFilter]="column.key !== 'roleName'"
            [nzShowFilter]="column.key === 'roleName'"
            [nzFilterMultiple]="true" 
            [nzFilterFn]="column.filterFn || null" 
            [nzFilters]="column.listOfFilter || []"
          >
            {{ column.title }}
          </th>
        }
      </tr>
    </thead>
    <tbody>
      @for (permission of sortTable.data; track $index) {
      <tr>
        @if (!isEditing[$index]) {
        <ng-container>
          <td>
            <lib-button (click)="handleEditClick($index)" [disabled]="!updatePermission" buttonType="primary" />
          </td>
          <td>{{ permission.roleName }}</td>
          <td>{{ permission.pageName }}</td>
          <td>
            <lib-tag [color]="permission.readFlag   ? 'green' : 'red'">{{permission.readFlag ? '是' : '否'}}</lib-tag>
          </td>
          <td>
            <lib-tag [color]="permission.createFlag ? 'green' : 'red'">{{permission.createFlag ? '是' : '否'}}</lib-tag>
          </td>
          <td>
            <lib-tag [color]="permission.updateFlag ? 'green' : 'red'">{{permission.updateFlag ? '是' : '否'}}</lib-tag>
          </td>
          <td>
            <lib-tag [color]="permission.deleteFlag ? 'green' : 'red'">{{permission.deleteFlag ? '是' : '否'}}</lib-tag>
          </td>
        </ng-container>
        }
        @else {
          <td>
            <div nz-flex [nzGap]="8">
              <lib-modal description="是否修改此權限資訊?"
              (nzOnOkClick)="handleEditOkClick($index)"
              iconType="check"></lib-modal>
              <lib-button (click)="handleEditClick($index)" [danger]="true" iconType="close"></lib-button>
            </div>
          </td>
          <td><lib-input [(ngModel)]="permission.roleName" [disabled]="true"></lib-input></td>
          <td><lib-input [(ngModel)]="permission.pageName" [disabled]="true"></lib-input></td>
          <td><lib-checkbox [(checked)]="permission.readFlag  " /></td>
          <td><lib-checkbox [(checked)]="permission.createFlag" /></td>
          <td><lib-checkbox [(checked)]="permission.updateFlag" /></td>
          <td><lib-checkbox [(checked)]="permission.deleteFlag" /></td>
        }
      </tr>
    }
    </tbody>
  </nz-table>
</div>