// ./app/shared/interceptor/auth.interceptor.ts
import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { tap } from 'rxjs/operators';
import { HttpResponse } from '@angular/common/http';

export const AuthInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router);
  const token = localStorage.getItem('token');
  
  if (token) {
    req = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`
      }
    });
  }

  return next(req).pipe(
    tap(event => {
      if (event instanceof HttpResponse) {
        const newToken = event.headers.get('X-New-Token');
        if (newToken) {
          localStorage.setItem('token', newToken);
        }
      }
    }),
    catchError(error => {
      if ([401, 403].includes(error.status)) {
        alert('登入逾時或權限不足，請重新登入');
        router.navigate(['/login']);
      }
      return throwError(() => error);
    })
  );
};
