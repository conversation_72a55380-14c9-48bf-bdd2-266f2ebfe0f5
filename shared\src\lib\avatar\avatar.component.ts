import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';

const colorList = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae'];

@Component({
  selector: 'lib-avatar',
  standalone: true,
  imports: [CommonModule, NzAvatarModule],
  template: `
    <nz-avatar
      [nzGap]="4"
      [ngStyle]="{ 'background-color': color }"
      [nzText]="text"
      nzSize="default"
      style="vertical-align: middle;"
    ></nz-avatar>
  `,
  styles: `
      div {
        margin-bottom: 16px;
      }
      button {
        margin-left: 8px;
      }
  `,
})
export class AvatarComponent {
  @Input() text = '';
  color = colorList[Math.floor(Math.random() * colorList.length)];
}
