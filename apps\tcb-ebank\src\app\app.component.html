@if (showSidebar) {
<nz-layout class="app-layout">
  <nz-sider class="menu-sidebar"
    nzCollapsible
    nzWidth="256px"
    nzBreakpoint="md"
    [(nzCollapsed)]="isCollapsed"
    [nzTrigger]="null">
    <div class="sidebar-logo">
      <a routerLink="/welcome">
        <img src="./assets/favicon.ico" alt="logo" class="logo">
        <h1>合庫銀行活動後台</h1>
      </a>
    </div>
    <ul nz-menu nzTheme="dark" nzMode="inline" [nzInlineCollapsed]="isCollapsed">
      <li
          nz-menu-item
          nz-tooltip
          nzTooltipPlacement="right"
          [nzTooltipTitle]="isCollapsed ? 'Home' : ''"
          nzMatchRouter
        >
          <span nz-icon nzType="home"></span>
          <span>Home</span>
          <a routerLink="/welcome">

          </a>
        </li>
      <li nz-submenu nzOpen nzTitle="系統管理" nzIcon="dashboard">
        <ul>
          <li nz-menu-item nzMatchRouter>
            <a routerLink="/event">活動管理</a>
          </li>
          <li nz-menu-item nzMatchRouter>
            <a routerLink="/customer">客戶填表管理</a>
          </li>
        </ul>
      </li>
    </ul>
  </nz-sider>
  <nz-layout>
    <nz-header>
      <div class="app-header">
        <span class="header-trigger" (click)="isCollapsed = !isCollapsed">
            <span class="trigger"
               nz-icon
               [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'"
            ></span>
        </span>
      </div>
    </nz-header>
    <nz-content>
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
  </nz-layout>
</nz-layout>
}
@else {
  <router-outlet></router-outlet>
}
