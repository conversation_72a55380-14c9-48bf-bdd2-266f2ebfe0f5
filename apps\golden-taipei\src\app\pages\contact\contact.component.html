<div class="container mx-auto px-4">
  <div
    class="grid grid-cols-3 justify-items-stretch items-center justify-between"
  >
    <div class="col-span-2 flex items-center gap-4">
      <h2 class="text-2xl font-medium mb-0">聯絡方式管理</h2>
      <lib-modal
        class="flex items-center"
        [modalConfig]="{
          title: '新增方式資訊',
          content: ContactEditComponent,
          okText: '新增',
          width: '500px'
        }"
        (formSubmit)="handleAddClick($event)"
        [shape]="'round'"
        [iconType]="'plus'"
        [actionType]="'dynamic'"
      >
        新增方式資訊
      </lib-modal>
    </div>
  </div>
  <br />
  <div class="w-full overflow-x-auto shadow-sm rounded-lg">
    <nz-table
      #contactTable
      [nzData]="contacts"
      nzTableLayout="fixed"
      [nzPageSize]="15"
      [nzSize]="'small'"
      class="min-w-full bg-white"
    >
      <thead>
        <tr class="bg-gray-50 border-b">
          @for (column of listOfColumns; track column.title) {
          <th
            [nzSortFn]="column.compare!"
            [nzWidth]="column.width"
            class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider"
          >
            {{ column.title }}
          </th>
          }
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-200">
        @for (contact of contacts; track contact.contactId) {
        <tr class="hover:bg-gray-50 transition-colors">
          <td>
            <div nz-flex [nzGap]="8">
              <lib-modal
                [iconType]="'edit'"
                [modalConfig]="{
                  title: '編輯聯絡資訊',
                  content: ContactEditComponent,
                  okText: '編輯',
                  width: '500px'
                }"
                [buttonType]="'primary'"
                [actionType]="'editContact'"
                [contact]="contact"
                (formSubmit)="handleEditOkClick($event)"
              ></lib-modal>
              <lib-modal
                [danger]="true"
                iconType="delete"
                buttonType="default"
                [description]="description"
                (nzOnOkClick)="handleDeleteClick(contact)"
              ></lib-modal>
              <ng-template #description>
                <p>
                  是否要刪除 <b>{{ contact.contactValue }} </b> ?
                </p>
              </ng-template>
            </div>
          </td>
          <td>{{ contact.contactValue }}</td>
        </tr>
        }
      </tbody>
    </nz-table>
  </div>
</div>
