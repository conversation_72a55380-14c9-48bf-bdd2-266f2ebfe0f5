import { Route } from '@angular/router';
import { BannerComponent } from './pages/banner/banner.component';
import { LoginComponent } from '@pages/login/login.component';
import { LogComponent } from '@pages/log/log.component';
import { PermissionComponent } from '@pages/permission/permission.component';
import { UserComponent } from '@pages/user/user.component';
import { HomeComponent } from '@pages/home/<USER>';
import { CategoryComponent } from './pages/category/category.component';
import { CategoryEditComponent } from './pages/category/category-edit/category-edit.component';
import { ContactComponent } from './pages/contact/contact.component';
import { MerchantComponent } from './pages/merchant/merchant.component';
import { MerchantIndexComponent } from './pages/merchant/merchant-index/merchant-index.component';
import { ProductComponent } from './pages/product/product.component';
import { ProductIndexComponent } from './pages/product/product-index/product-index.component';
import { BannerIndexComponent } from './pages/banner/banner-index/banner-index.component';
export const appRoutes: Route[] = [
  { path: 'login', component: LoginComponent },
  {
    path: 'log',
    component: LogComponent,
    data: { breadcrumb: '系統日誌', permission: 'log' },
  },
  {
    path: 'permission',
    component: PermissionComponent,
    data: { breadcrumb: '權限管理', permission: 'permission' },
  },
  {
    path: 'user',
    component: UserComponent,
    data: { breadcrumb: '使用者管理', permission: 'user' },
  },
  { path: 'home', component: HomeComponent, data: { permission: 'home' } },
  {
    path: 'contact',
    component: ContactComponent,
    data: { breadcrumb: '聯絡方式管理', permission: 'contact' },
  },
  {
    path: 'banner',
    data: { breadcrumb: '廣告橫幅管理', permission: 'banner' },
    children: [
      { path: '', component: BannerComponent },
      {
        path: ':bannerId',
        component: BannerIndexComponent,
        data: { breadcrumb: '廣告橫幅詳細資料' },
      },
    ],
  },
  {
    path: 'category',
    data: { breadcrumb: '分類管理', permission: 'category' },
    children: [
      { path: '', component: CategoryComponent },
      {
        path: ':categoryId',
        component: CategoryEditComponent,
        data: { breadcrumb: '分類詳細資料' },
      },
    ],
  },
  {
    path: 'merchant',
    data: { breadcrumb: '商家管理', permission: 'merchant' },
    children: [
      { path: '', component: MerchantComponent },
      {
        path: ':merchantId',
        component: MerchantIndexComponent,
        data: { breadcrumb: '商家詳細資料' },
      },
    ],
  },
  {
    path: 'product',
    data: { breadcrumb: '商品管理', permission: 'product' },
    children: [
      { path: '', component: ProductComponent },
      {
        path: ':productId',
        component: ProductIndexComponent,
        data: { breadcrumb: '商品詳細資料' },
      },
    ],
  },

  { path: '**', redirectTo: 'home' },
];
