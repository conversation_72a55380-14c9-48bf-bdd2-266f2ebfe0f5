<div class="login-container">
  <form nz-form [formGroup]="validateForm" class="login-form" (ngSubmit)="submitForm()">
      <img src="./logo.png" alt="Logo" class="logo">
      <nz-form-item>
        <nz-form-control nzErrorTip="請輸入您的帳號!">
          <nz-input-group nzPrefixIcon="user">
            <input type="text" nz-input formControlName="account" placeholder="Account" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control nzErrorTip="請輸入您的密碼!">
          <nz-input-group nzPrefixIcon="lock" [nzSuffix]="suffixTemplate">
            <input [type]="passwordVisible ? 'text' : 'password'" nz-input formControlName="password" placeholder="Password" />
          </nz-input-group>
          <ng-template #suffixTemplate>
            <nz-icon
              class="ant-input-password-icon"
              [nzType]="passwordVisible ? 'eye-invisible' : 'eye'"
              (click)="passwordVisible = !passwordVisible"
            ></nz-icon>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
    <button nz-button class="login-form-button login-form-margin" [nzType]="'primary'">登入</button>
  </form>
</div>
