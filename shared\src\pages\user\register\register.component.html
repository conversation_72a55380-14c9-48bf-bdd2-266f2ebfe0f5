<form nz-form [formGroup]="validateForm" (ngSubmit)="submitForm()">
  <nz-form-item>
    <nz-form-label [nzSm]="10" [nzXs]="24" nzRequired nzFor="account">系統帳號</nz-form-label>
    <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="請輸入您的帳號!">
      <input nz-input formControlName="account" id="account" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="10" [nzXs]="24" nzFor="password" nzRequired>系統使用者密碼</nz-form-label>
    <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="請輸入您的密碼!">
      <input
        nz-input
        type="password"
        id="password"
        formControlName="password"
        (ngModelChange)="updateConfirmValidator()"
      />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="10" [nzXs]="24" nzFor="checkPassword" nzRequired>確認密碼</nz-form-label>
    <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="errorTpl">
      <input nz-input type="password" formControlName="checkPassword" id="checkPassword" />
      <ng-template #errorTpl let-control>
        @if (control.errors?.['required']) {
          請輸入您的密碼!
        }
        @if (control.errors?.['confirm']) {
          您輸入的兩次密碼不一致！
        }
      </ng-template>
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label
      [nzSm]="10"
      [nzXs]="24"
      nzFor="userName"
      nzRequired
      nzTooltipTitle="登入系統請用系統帳號"
    >
      <span>使用者暱稱</span>
    </nz-form-label>
    <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="請輸入使用者暱稱!">
      <input nz-input formControlName="userName" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item nz-row class="register-area">
    <nz-form-label [nzSm]="10" [nzXs]="24" nzFor="role" nzRequired>系統角色</nz-form-label>
    <nz-form-control [nzSpan]="14" nzErrorTip="請選擇系統角色!">
      <nz-select formControlName="role" [nzSize]="'large'" [nzDisabled]="isAdmin">
        @for (role of roles; track $index) {
          <nz-option [nzValue]="role.role" [nzLabel]="role.roleName"></nz-option>
        }
      </nz-select>
    </nz-form-control>
  </nz-form-item>
</form>
