import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';

@Component({
  selector: 'lib-dropdown-menu-search',
  standalone: true,
  imports: [CommonModule, FormsModule, NzButtonModule, NzInputModule],
  templateUrl: './dropdown-menu-search.component.html',
  styleUrl: './dropdown-menu-search.component.scss',
})
export class DropdownMenuSearchComponent {
  @Input() listOfColumns: any[] = [];
  @Input() dataTable: any[] = [];
  @Input() currentIndex = 0;

  @Output() filteredEvent = new EventEmitter<any[]>();

  filteredTable: any[] = [];

  OnInit(): void {
    this.filteredTable = this.dataTable;
  }

  reset(): void {
    this.listOfColumns.forEach((column) => {
      if (column.searchValue !== undefined) {
        column.searchValue = '';
      }
    });
    this.search(this.currentIndex);
  }

  search(index: number): void {
    //關閉此搜尋視窗
    this.listOfColumns[index].visible = false;

    let allEmpty = true;
    this.filteredTable = this.dataTable;

    //多欄位搜尋邏輯
    for (const element of this.listOfColumns) {
      if (!element.searchValue) continue;
      else {
        allEmpty = false;
        this.filteredTable = this.filteredTable.filter((item: any) => {
          return item[element.key]
            ?.toString()
            .toLowerCase()
            .includes(element.searchValue.toLowerCase());
        });
      }
    }
    if (allEmpty) {
      this.filteredTable = this.dataTable;
    }
    this.filteredEvent.emit(this.filteredTable);
  }
}
