{"name": "shared-library", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "shared/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "shared/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}, "storybook": {"executor": "@storybook/angular:start-storybook", "options": {"port": 4400, "configDir": "shared/.storybook", "browserTarget": "shared-library:build-storybook", "compodoc": false, "styles": ["ng-zorro-antd/style/default.less", "ng-zorro-antd/style/index.css"]}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@storybook/angular:build-storybook", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/ng-zorro-shared", "configDir": "shared/.storybook", "browserTarget": "shared-library:build-storybook", "compodoc": false}, "configurations": {"ci": {"quiet": true}}}}}