import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class LoginService {
  constructor(
    private http: HttpClient, 
    private router: Router,
    @Inject('API_URL') private baseApiUrl: string
  ) {}

  login(account: string, password: string): Observable<any> {
    return this.http.post<any>(`${this.baseApiUrl}/User/login`, { Account: account, Password: password })
      .pipe(
        tap(response => {
          localStorage.setItem('token', response.token);
        })
      );
  }

  logout() {
    localStorage.removeItem('token');
    this.router.navigate(['/login']);
  }

  isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    // 在這裡添加額外的邏輯來驗證 token 是否有效
    return !!token;
  }
}
