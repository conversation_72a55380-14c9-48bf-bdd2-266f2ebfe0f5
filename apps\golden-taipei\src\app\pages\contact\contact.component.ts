import { Component, OnInit } from '@angular/core';
import { ContactService } from './contact.service';
import { Contact } from '@models/contact.model';
import { ContactEditComponent } from './contact-edit/contact-edit.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ModalComponent } from '@lib/modal/modal.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzFlexModule } from 'ng-zorro-antd/flex';
@Component({
  selector: 'app-contact',
  templateUrl: './contact.component.html',
  imports: [ModalComponent, NzTableModule, NzFlexModule],
})
export class ContactComponent implements OnInit {
  constructor(
    private contactService: ContactService,
    private message: NzMessageService
  ) {}
  ContactEditComponent = ContactEditComponent;
  contacts: Contact[] = [];
  listOfColumns: any[] = [
    {
      title: '操作',
      width: '20%',
    },
    {
      title: '聯絡資訊',
      compare: (a: Contact, b: Contact) =>
        a.contactValue.localeCompare(b.contactValue),
      width: '60%',
    },
  ];
  searchValue = '';
  ngOnInit(): void {
    this.contactService.getContact().subscribe((response) => {
      this.contacts = response.data;
    });
  }
  handleDeleteClick(contact: Contact): void {
    this.contactService
      .deleteContact(contact.contactId)
      .subscribe((response) => {
        if (response.success) {
          this.message.success('刪除聯絡資訊成功');
        } else {
          this.message.error('刪除聯絡資訊失敗' + response.message);
        }
        this.ngOnInit();
      });
  }

  handleAddClick(contact: Contact): void {
    this.contactService.createContact(contact).subscribe((response) => {
      if (response.success) {
        this.message.success('新增聯絡資訊成功');
      } else {
        this.message.error('新增聯絡資訊失敗' + response.message);
      }
      this.ngOnInit();
    });
  }

  handleEditOkClick(contact: Contact): void {
    this.contactService.updateContact(contact).subscribe((response) => {
      if (response.success) {
        this.message.success('編輯聯絡資訊成功');
      } else {
        this.message.error('編輯聯絡資訊失敗' + response.message);
      }
      this.ngOnInit();
    });
  }
}
