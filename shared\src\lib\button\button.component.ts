import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
  selector: 'lib-button',
  standalone: true,
  imports: [CommonModule, NzButtonModule, NzIconModule],
  template: `
    <button
      nz-button
      [nzType]="buttonType"
      [nzShape]="shape"
      (click)="onButtonClick($event)"
      [nzDanger]="danger"
      [disabled]="disabled"
      [name]="iconType"
      role="button"
    >
      <span nz-icon [nzType]="iconType" style="display: block;"></span>
      <ng-content></ng-content>
    </button>
  `,
  styles: [
    `
      .ant-btn {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
      }
    `
  ]
})
export class ButtonComponent {
  @Input() buttonType: 'default' | 'primary' | 'dashed' | 'link' = 'default';
  @Input() shape: 'circle' | 'round' | null = 'circle';
  @Input() danger = false;
  @Input() iconType = 'edit';
  @Input() disabled = false;

  @Output() nzButtonClick = new EventEmitter<MouseEvent>();

  onButtonClick(event: MouseEvent): void {
    this.nzButtonClick.emit(event);
  }
}
