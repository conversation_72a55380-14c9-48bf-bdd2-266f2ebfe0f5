<div class="container mt-5">
  <div nz-row>
    <div nz-flex [nzGap]="8">
      <h2 class="text-2xl font-semibold mb-0">{{title}}</h2>
      <lib-modal [actionType]="'dynamic'" [modalConfig]="{
        title: '新增使用者',
        content: RegisterComponent,
        okText: '新增使用者',
      }" buttonType="primary" iconType="plus" [shape]="'round'"
        (formSubmit)="handleAddClick($event)">
        新增系統帳號
      </lib-modal>
    </div>
    <br>
    <nz-table #sortTable [nzShowSizeChanger]="filteredUsers.length > 10" nzTableLayout="fixed" [nzData]="filteredUsers" [nzPageSize]=10 [nzSize]="'small'">
    <thead>
      <tr>
        @for (column of listOfColumns; track $index) {
          <th [nzSortFn]="column.compare"
          [nzCustomFilter]="!column.filterFn"
          [nzShowFilter]="column.filterFn"
          [nzFilterMultiple]="true" 
          [nzFilterFn]="column.filterFn || null" 
          [nzFilters]="column.listOfFilter || []"
          >
          {{ column.title }}
          @if (column.searchValue !== undefined) {
            <nz-filter-trigger [(nzVisible)]="column.visible"
              [nzActive]="column.searchValue.length > 0" [nzDropdownMenu]="menu" (nzVisibleChange)="setCurrentIndex($index)">
              <span nz-icon nzType="search"></span>
            </nz-filter-trigger>
          }
          </th>
        }
      </tr>
    </thead>
    <tbody>
      @for (user of sortTable.data; track $index) {
        <tr>
            <td>
              <div nz-flex [nzGap]="8">
                <lib-modal [actionType]="'editUser'" [modalConfig]="{
                  title: '修改使用者',
                  content: RegisterComponent,
                  okText: '修改使用者',
                }" [enabled]="updateUser || user.account === userAccount" iconType='edit' [user]="user"
                  (formSubmit)="handleEditClick(user.account, $event)"></lib-modal>
                <ng-container>
                  @if (user.roleName !== 'Administrator') {
                    <lib-modal [enabled]="deleteUser" [actionType]="'simple'"
                      buttonType="default" (nzOnOkClick)="handleDeleteClick(user.account)" [danger]="true"
                      description="是否刪除此系統帳號?" />
                  }
                </ng-container>
              </div>
            </td>
            <td>{{ user.account }}</td>
            <td>{{ user.userName }}</td>
            <td>{{ user.roleName }}</td>
            <td>{{ user.loginTime | date:'yyyy-MM-dd HH:mm:ss' }}</td>
            <td>{{ user.modTime | date:'yyyy-MM-dd HH:mm:ss' }}</td>
            <td><lib-tag [color]="user.enable ? 'green' : 'red'">{{user.enable ? '是' : '否'}}</lib-tag></td>
        </tr>
      }
    </tbody>
  </nz-table>
</div>

<nz-dropdown-menu #menu="nzDropdownMenu">
  <lib-dropdown-menu-search [listOfColumns]="listOfColumns" [currentIndex]="currentIndex" [dataTable]="users"
    (filteredEvent)="newTable($event)"></lib-dropdown-menu-search>
</nz-dropdown-menu>