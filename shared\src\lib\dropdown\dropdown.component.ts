import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzMenuModule } from 'ng-zorro-antd/menu';
@Component({
  selector: 'lib-dropdown',
  standalone: true,
  imports: [CommonModule, NzButtonModule, NzDropDownModule, NzMenuModule],
  template: `
    <div>
      <button
        nz-button
        nz-dropdown
        nzTrigger="click"
        [nzDropdownMenu]="menu"
        [nzDisabled]="disabled"
        [disabled]="disabled"
      >
        {{ selectedItem || '請選擇' }}
      </button>
      <nz-dropdown-menu #menu="nzDropdownMenu">
        <ul nz-menu>
          @for (item of dataList; track item; let i = $index) {
          <li
            nz-menu-item
            tabindex="0"
            (click)="onItemSelect(i)"
            (keydown.enter)="onItemSelect(i)"
          >
            {{ item }}
          </li>
          }
        </ul>
      </nz-dropdown-menu>
    </div>
  `,
  styles: [
    `
      [nz-button] {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    `,
  ],
})
export class DropdownComponent implements OnInit {
  @Input() disabled = false;
  @Input() dataList: string[] = [];
  @Input() defaultIndex = -1;
  @Output() selectedIndex = new EventEmitter<number>();
  selectedItem: string | null = null;

  ngOnInit() {
    if (this.defaultIndex >= 0 && this.defaultIndex < this.dataList.length) {
      this.selectedItem = this.dataList[this.defaultIndex];
      this.selectedIndex.emit(this.defaultIndex);
    }
  }

  onItemSelect(index: number): void {
    this.selectedItem = this.dataList[index];
    this.selectedIndex.emit(index);
  }
}
