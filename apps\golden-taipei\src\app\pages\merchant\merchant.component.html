<section class="container mx-auto px-4">
  <header class="bg-white space-y-4 p-4 sm:px-8 sm:py-6 lg:p-4 xl:px-8 xl:py-6">
    <div class="flex items-center justify-between">
      <h2 class="text-2xl font-semibold mb-0 text-slate-900">商家管理</h2>
      <a
        [routerLink]="['/merchant/new']"
        class="hover:bg-primary/80 group flex items-center rounded-md bg-primary text-white text-sm font-medium pl-2 pr-3 py-2 shadow-sm"
      >
        <svg
          width="20"
          height="20"
          fill="currentColor"
          class="mr-2"
          aria-hidden="true"
        >
          <path
            d="M10 5a1 1 0 0 1 1 1v3h3a1 1 0 1 1 0 2h-3v3a1 1 0 1 1-2 0v-3H6a1 1 0 1 1 0-2h3V6a1 1 0 0 1 1-1Z"
          />
        </svg>
        新增商家
      </a>
    </div>
    <form class="group relative" (ngSubmit)="filterMerchants()">
      <svg
        width="20"
        height="20"
        fill="currentColor"
        class="absolute left-3 top-1/2 -mt-2.5 text-slate-400 pointer-events-none group-focus-within:text-primary"
        aria-hidden="true"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
        />
      </svg>
      <input
        class="focus:ring-2 focus:ring-primary focus:outline-none appearance-none w-full text-sm leading-6 text-slate-900 placeholder-slate-400 rounded-md py-2 pl-10 ring-1 ring-slate-200 shadow-sm"
        type="text"
        aria-label="篩選商家"
        placeholder="篩選商家..."
        [(ngModel)]="searchTerm"
        (ngModelChange)="filterMerchants()"
        name="searchTerm"
      />
    </form>
  </header>
  <ul
    class="bg-slate-50 p-4 sm:px-8 sm:pt-6 sm:pb-8 lg:p-4 xl:px-8 xl:pt-6 xl:pb-8 grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 text-sm leading-6"
  >
    @for (merchant of filteredMerchants$ | async; track merchant.merchantId) {
    <li>
      <a
        [routerLink]="['/merchant/', merchant.merchantId]"
        class="hover:bg-primary hover:ring-primary hover:shadow-md group rounded-md p-3 bg-white ring-1 ring-slate-200 shadow-sm flex flex-col items-center h-[280px] max-w-[220px] mx-auto overflow-hidden"
      >
        <div class="mb-3 flex-grow flex items-center justify-center w-full">
          <lib-image
            [src]="imageUrl + '/merchant/' + merchant.imagePath"
            [alt]="merchant.merchantName"
            class="w-[180px] max-w-full h-[200px] object-contain rounded-md ring-2 ring-white"
            loading="lazy"
          />
        </div>
        <div class="text-center w-full">
          <h3
            class="group-hover:text-white font-semibold text-slate-900 truncate text-lg"
          >
            {{ merchant.merchantName }}
          </h3>
        </div>
      </a>
    </li>
    }
    <li class="flex">
      <a
        [routerLink]="['/merchant/new']"
        class="hover:border-primary hover:border-solid hover:bg-white hover:text-primary group w-full flex flex-col items-center justify-center rounded-md border-2 border-dashed border-slate-300 text-lg leading-6 text-slate-900 font-medium py-3 h-[280px] max-w-[220px] mx-auto overflow-hidden"
      >
        <svg
          class="group-hover:text-primary mb-1 text-slate-400"
          width="20"
          height="20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            d="M10 5a1 1 0 0 1 1 1v3h3a1 1 0 1 1 0 2h-3v3a1 1 0 1 1-2 0v-3H6a1 1 0 1 1 0-2h3V6a1 1 0 0 1 1-1Z"
          />
        </svg>
        新增商家
      </a>
    </li>
  </ul>
</section>
