import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiResponse } from '@models/api-response.model';
import { Category } from '@models/category.model';

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  constructor(private http: HttpClient) {}
  private apiUrl = environment.apiUrl + '/category';

  getCategory(categoryId?: string): Observable<ApiResponse<Category[]>> {
    return this.http.get<ApiResponse<Category[]>>(
      this.apiUrl + (categoryId ? '?categoryId=' + categoryId : '')
    );
  }

  createCategory(category: any): Observable<ApiResponse<Category>> {
    return this.http.post<ApiResponse<Category>>(this.apiUrl, category);
  }

  updateCategory(category: any): Observable<ApiResponse<Category>> {
    return this.http.put<ApiResponse<Category>>(this.apiUrl, category);
  }

  deleteCategory(categoryId: string): Observable<ApiResponse<Category>> {
    return this.http.delete<ApiResponse<Category>>(
      this.apiUrl + '/' + categoryId
    );
  }
}
