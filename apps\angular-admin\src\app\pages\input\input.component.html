<div class="flex flex-col gap-4">
    <div class="flex flex-row gap-4">
        <nz-card style="width:100%;">
            <div class="flex flex-col gap-4">
                <div>
                    純文本輸入
                    <div class="flex flex-row gap-4">
                        <lib-input placeholder="Default" class="flex-1"></lib-input>
                        <lib-input [disabled]="true" placeholder="Disabled" class="flex-1"></lib-input>
                        <lib-search class="flex-1"></lib-search>
                    </div>
                </div>
                <div>
                    無效輸入警告
                    <form nz-form [formGroup]="validateForm" class="login-form">
                        <nz-form-item>
                            <nz-form-control nzErrorTip="請輸入您的密碼!">
                                <nz-input-group nzPrefixIcon="lock" [nzSuffix]="suffixTemplate">
                                    <input [type]="passwordVisible ? 'text' : 'password'" nz-input formControlName="password" placeholder="Password" />
                                </nz-input-group>
                                <ng-template #suffixTemplate>
                                    <nz-icon
                                    class="ant-input-password-icon"
                                    [nzType]="passwordVisible ? 'eye-invisible' : 'eye'"
                                    (click)="passwordVisible = !passwordVisible"
                                    ></nz-icon>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </form>
                </div>
                <div>
                    數字輸入
                    <lib-input-number [size]="'default'"></lib-input-number>
                </div>
                <div>
                    按鈕
                    <br>
                    <div class="flex flex-row gap-4">
                        <lib-button></lib-button>
                        <lib-button [buttonType]="'primary'"></lib-button>
                        <lib-button [buttonType]="'dashed'"></lib-button>
                        <lib-button [buttonType]="'link'"></lib-button>
                        <lib-button [danger]="true"></lib-button>
                        <lib-button [disabled]="true"></lib-button>
                    </div>
                </div>
                <div>
                    按鈕文字
                    <br>
                    <div class="flex flex-row gap-4">
                        <lib-button [shape]="'round'">按鈕文字</lib-button>
                        <lib-button [buttonType]="'primary'" [shape]="'round'">主要按鈕</lib-button>
                        <lib-button [buttonType]="'dashed'" [shape]="'round'">虛線按鈕</lib-button>
                        <lib-button [buttonType]="'link'" [shape]="'round'">連結按鈕</lib-button>
                        <lib-button [shape]="'round'" [danger]="true">危險按鈕</lib-button>
                        <lib-button [shape]="'round'" [disabled]="true">禁用按鈕</lib-button>
                    </div>
                </div>
            </div>
        </nz-card>
        <nz-card style="width:100%;">
            <div class="flex flex-col gap-4">
                <div>
                    單選+搜尋
                    <br>
                    <nz-select nzShowSearch nzAllowClear nzPlaceHolder="Select a person">
                        <nz-option nzLabel="Jack" nzValue="jack"></nz-option>
                        <nz-option nzLabel="Lucy" nzValue="lucy"></nz-option>
                        <nz-option nzLabel="Tom" nzValue="tom"></nz-option>
                    </nz-select>
                </div>
                <div>
                    多選
                    <br>
                    <nz-form-control [nzSpan]="24" [nzLg]="20" nzErrorTip="請選擇商品分類!">
                        <nz-select
                            [nzMaxTagCount]="3"
                            nzSize="large"
                            nzMode="multiple"
                            [nzMaxTagPlaceholder]="tagPlaceHolder"
                            nzPlaceHolder="請選擇商品分類"
                        >
                        @for (category of categories; track category.categoryId) {
                            <nz-option
                            [nzValue]="category.categoryId"
                                    [nzLabel]="category.categoryName"
                                >
                            </nz-option>
                        }
                    </nz-select>
                    <ng-template #tagPlaceHolder let-selectedList>
                        以及其他 {{ selectedList.length }} 項
                    </ng-template>
                </nz-form-control>
                </div>
                <div>
                    日期選擇器<br>
                    <lib-date-picker></lib-date-picker>
                </div>
                <div>
                    日期(期間)選擇器<br>
                    <lib-range-picker></lib-range-picker>
                </div>
            </div>
        </nz-card>
    </div>
    <nz-card style="width:100%;">
        <div>
            WYSIWYG 編輯器
            <lib-quill></lib-quill>
        </div>
    </nz-card>
</div>