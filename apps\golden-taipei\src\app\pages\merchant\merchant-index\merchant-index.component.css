.tag-list {
  background-color: #fff;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px;
  min-height: 40px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin: 8px 0;
  transition: all 0.3s ease;
}

.tag-box {
  cursor: move;
  display: inline-block;
}

.large-tag {
  color: #fff;
  padding: 4px 12px;
  background: #108ee9;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin: 0;
}

.preview-tag {
  color: #fff;
  padding: 4px 12px;
  background: #108ee9;
  border: 1px solid #000;
  border-radius: 4px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.placeholder-tag {
  width: 100px;
  height: 28px;
  background: #f0f0f0;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .tag-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
