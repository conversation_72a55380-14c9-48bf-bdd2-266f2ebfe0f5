import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  NonNullableFormBuilder,
  ValidatorFn,
  Validators
} from '@angular/forms';
import { RoleSelectService, Role } from '@services/role-select.service';
import { User } from '@models/user.model';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { ReactiveFormsModule } from '@angular/forms';
import { NzInputModule } from 'ng-zorro-antd/input';
@Component({
  selector: 'lib-register',
  imports: [
    NzFormModule,
    NzSelectModule,
    ReactiveFormsModule,
    NzInputModule,
  ],
  templateUrl: './register.component.html',
  styles: `
    [nz-form] {
      max-width: 600px;
    }

    .ant-select.ant-select-in-form-item.phone-select {
      width: 70px;
    }

    .register-area {
      margin-bottom: 8px;
    }
  
  `
})
export class RegisterComponent implements OnInit {
  @Input() user: User = new User(); // 新增的輸入屬性
  @Output() formSubmit = new EventEmitter<any>();

  roles: Role[] = [];
  roleIndex = -1;
  isAdmin = false;

  validateForm: FormGroup<{
    account: FormControl<string>;
    password: FormControl<string>;
    checkPassword: FormControl<string>;
    userName: FormControl<string>;
    role: FormControl<number>;
  }>;

  constructor(private fb: NonNullableFormBuilder, private roleSelectService: RoleSelectService) {
    this.validateForm = this.fb.group({
      account: ['', [Validators.required]],
      password: ['', [Validators.required]],
      checkPassword: ['', [Validators.required, this.confirmationValidator]],
      userName: ['', [Validators.required]],
      role: [-1, [Validators.required, Validators.min(0)]]
    });

    this.roleSelectService.getRoles().subscribe(roles => {
      this.roles = roles.data;
    });
  }

  ngOnInit(): void {
    // 如果是修改使用者，則將 account 設為 disabled
    if (this.user && this.user.account) {
      this.roleIndex = this.user.role - 1;
      this.validateForm.patchValue({ account: this.user.account });
      this.validateForm.patchValue({ role: this.roleIndex + 1 });
      this.validateForm.patchValue({ userName: this.user.userName });
      this.validateForm.get('account')?.disable();
      this.isAdmin = this.roleIndex === 0;
    }
  }

  selectedRoleIndex(index: number): void {
    this.roleIndex = index;
    this.validateForm.patchValue({ role: index + 1 });
  }

  submitForm(): void {
    if (this.validateForm.valid) {
      this.formSubmit.emit(this.validateForm.value);
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  updateConfirmValidator(): void {
    /** wait for refresh value */
    Promise.resolve().then(() => this.validateForm.controls.checkPassword.updateValueAndValidity());
  }

  confirmationValidator: ValidatorFn = (control: AbstractControl): { [s: string]: boolean } => {
    if (!control.value) {
      return { required: true };
    } else if (control.value !== this.validateForm.controls.password.value) {
      return { confirm: true, error: true };
    }
    return {};
  };
}
