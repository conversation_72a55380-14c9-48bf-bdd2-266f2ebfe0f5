{"name": "pilot-logo-<PERSON><PERSON>", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/pilot-logo-Admin/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/pilot-logo-Admin", "index": "apps/pilot-logo-Admin/src/index.html", "browser": "apps/pilot-logo-Admin/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/pilot-logo-Admin/tsconfig.app.json", "inlineStyleLanguage": "less", "assets": [{"glob": "**/*", "input": "apps/pilot-logo-Admin/public"}, {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["apps/pilot-logo-Admin/src/styles.less"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "pilot-logo-Admin:build:production"}, "development": {"buildTarget": "pilot-logo-Admin:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "pilot-logo-Admin:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/pilot-logo-Admin/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "pilot-logo-Admin:build", "staticFilePath": "dist/apps/pilot-logo-Admin/browser", "spa": true}}}}