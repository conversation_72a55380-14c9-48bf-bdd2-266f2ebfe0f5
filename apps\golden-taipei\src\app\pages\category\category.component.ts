import { Component, OnInit } from '@angular/core';
import { CategoryService } from './category.service';
import { Subject } from 'rxjs';
import { Category } from '@models/category.model';
import { CategoryEditComponent } from './category-edit/category-edit.component';
import { environment } from '../../../environments/environment';
import { RouterModule } from '@angular/router';
import { ImageComponent } from '@lib/image/image.component';
import { FormsModule } from '@angular/forms';
@Component({
  imports: [RouterModule, ImageComponent, FormsModule],
  templateUrl: './category.component.html',
})
export class CategoryComponent implements OnInit {
  constructor(private categoryService: CategoryService) {}
  CategoryEditComponent = CategoryEditComponent;
  imageUrl = environment.imageUrl + '/category/';
  categorys: Category[] = [];
  isEditing: boolean[] = [];
  searchTerms$ = new Subject<string>();
  searchTerm = '';
  filteredCategorys: Category[] = [];
  listOfColumns = [
    {
      title: '操作',
      width: '30%',
    },
    {
      title: '標籤名稱',
      compare: (a: Category, b: Category) =>
        a.categoryName.localeCompare(b.categoryName),
      width: '40%',
    },
    {
      title: '商品數',
      width: '30%',
    },
  ];

  ngOnInit(): void {
    this.loadCategory();

    // 訂閱搜尋詞變化
    this.searchTerms$.subscribe((term) => {
      this.searchTerm = term;
      this.performSearch();
    });
  }

  loadCategory() {
    this.categoryService.getCategory().subscribe((response) => {
      this.categorys = response.data;
      this.filteredCategorys = response.data;
    });
  }

  filterCategorys() {
    this.searchTerms$.next(this.searchTerm);
  }

  performSearch() {
    if (this.searchTerm.trim() === '') {
      this.filteredCategorys = this.categorys;
    } else {
      this.filteredCategorys = this.categorys.filter((category) =>
        category.categoryName
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase())
      );
    }
  }
}
