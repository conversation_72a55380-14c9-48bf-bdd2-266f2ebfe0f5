import { Component, OnInit } from '@angular/core';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { CategoryService } from '../category.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Router, ActivatedRoute, ParamMap } from '@angular/router';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { Category } from '@models/category.model';
import { environment } from '../../../../environments/environment';
import { NzFormModule } from 'ng-zorro-antd/form';
import { ModalComponent } from '@lib/modal/modal.component';
import { UploadImageComponent } from '@lib/upload-image/upload-image.component';
import { InputComponent } from '@lib/input/input.component';
import { ReactiveFormsModule } from '@angular/forms';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-category-edit',
  templateUrl: './category-edit.component.html',
  imports: [
    NzFormModule,
    ModalComponent,
    UploadImageComponent,
    InputComponent,
    ReactiveFormsModule,
    NzFlexModule,
    NzToolTipModule,
  ],
})
export class CategoryEditComponent implements OnInit {
  constructor(
    private categoryService: CategoryService,
    private message: NzMessageService,
    private router: Router,
    private route: ActivatedRoute
  ) {}
  fileList: NzUploadFile[] = [];
  validateForm = new FormGroup({
    categoryId: new FormControl<number>(-1),
    imagePath: new FormControl<string | null>(null, [Validators.required]),
    categoryName: new FormControl<string | null>(null, [Validators.required]),
  });
  enabledDelete = false;
  categoryId: string | null = null;
  category: Category | null = null;

  ngOnInit() {
    this.route.paramMap.subscribe((params: ParamMap) => {
      this.categoryId = params.get('categoryId');
      if (this.categoryId && this.categoryId !== 'new') {
        this.categoryService
          .getCategory(this.categoryId)
          .subscribe((response) => {
            this.category = response.data[0];
            if (this.category) {
              this.validateForm.patchValue({
                categoryId: this.category.categoryId,
                imagePath: this.category.imagePath,
                categoryName: this.category.categoryName,
              });
              this.enabledDelete = (this.category?.productCount ?? 0) === 0;
              // 綁定圖片
              this.fileList = [
                {
                  uid: this.category.categoryId.toString(),
                  name: this.category.categoryName,
                  url:
                    environment.imageUrl +
                    '/category/' +
                    this.category.imagePath,
                },
              ];
            }
          });
      }
    });
  }

  uploadImage(filename: string) {
    this.validateForm.patchValue({ imagePath: filename });
    this.validateForm.get('imagePath')?.updateValueAndValidity();
  }

  submitForm() {
    if (this.validateForm.valid) {
      const formValue = { ...this.validateForm.value };

      if (this.categoryId && this.categoryId !== 'new') {
        this.categoryService.updateCategory(formValue).subscribe((response) => {
          if (response.success) {
            this.message.success('更新商品分類資料成功');
            this.router.navigate(['/category']);
          }
        });
      } else {
        this.categoryService.createCategory(formValue).subscribe((response) => {
          if (response.success) {
            this.message.success('新增商品分類資料成功');
            this.router.navigate(['/category']);
          }
        });
      }
    }
  }

  handleDeleteClick() {
    if (this.categoryId) {
      this.categoryService
        .deleteCategory(this.categoryId)
        .subscribe((response) => {
          if (response.success) {
            this.message.success('刪除商品分類資料成功');
            this.router.navigate(['/category']);
          }
        });
    }
  }
}
