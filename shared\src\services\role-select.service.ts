import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiResponse } from '../models/api-response.model';
import { Observable } from 'rxjs';

export interface Role {
  role: number;
  roleName: string;
}

@Injectable({
  providedIn: 'root'
})
export class RoleSelectService {
  private apiUrl: string;
  constructor(private http: HttpClient, @Inject('API_URL') private baseApiUrl: string) {
    this.apiUrl = `${this.baseApiUrl}/User`;
  }

  getRoles() : Observable<ApiResponse<Role[]>> {
    return this.http.get<ApiResponse<Role[]>>(this.apiUrl + '/GetAllRoles');
  }
}
