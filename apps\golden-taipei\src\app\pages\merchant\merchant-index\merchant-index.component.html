<form nz-form [nzLayout]="'horizontal'">
  <nz-form-item>
    <nz-form-label [nzSpan]="24" [nzLg]="4" nzRequired nzFor="merchantName"
      >商家名稱</nz-form-label
    >
    <nz-form-control
      nzErrorTip="請輸入商家名稱!"
      [nzSpan]="24"
      [nzLg]="20"
      nzErrorTip="請輸入商家名稱!"
    >
      <lib-input [formControl]="validateForm.controls.merchantName" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label
      [nzSpan]="24"
      [nzLg]="4"
      nzRequired
      nzFor="imagePath"
      nzTooltipTitle="欲上傳照片，請先刪除現有照片"
    >
      商家封面照
    </nz-form-label>
    <nz-form-control [nzSpan]="24" [nzLg]="20">
      <lib-upload-image
        [fileList]="fileList"
        uploadFolder="merchant"
        (uploadEvent)="uploadImage($event)"
      />
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-label [nzSpan]="24" [nzLg]="4" nzFor="categories"
      >商家分類排序(拖曳排序)</nz-form-label
    >
    <nz-form-control [nzSpan]="24" [nzLg]="20">
      <div
        nz-flex
        [nzGap]="8"
        cdkDropList
        class="tag-list"
        cdkDropListOrientation="mixed"
        [cdkDropListData]="categories"
        (cdkDropListDropped)="drop($event)"
      >
        @for (category of categories; track category) {
        <div cdkDrag [cdkDragData]="category" class="tag-box">
          <!-- 拖曳時的預覽元素 -->
          <div *cdkDragPreview class="preview-tag">
            {{ category.categoryName }}
          </div>
          <!-- 拖曳時的佔位元素 -->
          <div *cdkDragPlaceholder class="placeholder-tag"></div>
          <!-- 原有的標籤內容 -->
          <p class="large-tag">
            {{ category.categoryName }}
          </p>
        </div>
        }
      </div>
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <nz-form-control [nzOffset]="0" [nzSpan]="24" [nzLg]="20">
      <div nz-flex [nzGap]="30">
        <lib-modal
          description="是否儲存此商家?"
          [modalConfig]="{
          validateForm: validateForm,
        }"
          buttonType="primary"
          shape="round"
          [iconType]="'save'"
          (nzOnOkClick)="submitForm()"
        >
          儲存商家
        </lib-modal>
        <lib-modal
          description="是否刪除此商家?"
          [danger]="true"
          shape="round"
          [iconType]="'delete'"
          (nzOnOkClick)="handleDeleteClick()"
        >
          刪除商家
        </lib-modal>
      </div>
    </nz-form-control>
  </nz-form-item>
</form>
