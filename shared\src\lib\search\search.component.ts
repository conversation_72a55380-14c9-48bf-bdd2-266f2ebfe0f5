import { Component, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { FormsModule } from '@angular/forms';
@Component({
  selector: 'lib-search',
  standalone: true,
  imports: [CommonModule, NzInputModule, NzIconModule, FormsModule],
  template: `
    <nz-input-group [nzSuffix]="suffixIconSearch">
      <input
        type="text"
        nz-input
        placeholder="搜尋..."
        [(ngModel)]="searchTerm"
        (ngModelChange)="onSearch()"
      />
    </nz-input-group>
    <ng-template #suffixIconSearch>
      <span nz-icon nzType="search"></span>
    </ng-template>
  `,
})
export class SearchComponent {
  searchTerm = '';
  @Output() searchEmitter: EventEmitter<string> = new EventEmitter<string>();

  onSearch() {
    this.searchEmitter.emit(this.searchTerm);
  }
}
